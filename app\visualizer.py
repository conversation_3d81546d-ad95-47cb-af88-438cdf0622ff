"""Visdom wrapper with matplotlib and plotly support."""

import visdom
import plotly.io as pio
import matplotlib.pyplot as plt
from pathlib import Path
from typing import Optional, Union
from . import config


class Visualizer:
    """Wrapper class for Visdom visualization with export functionality."""
    
    def __init__(self):
        """Initialize Visdom connection."""
        self.viz = visdom.Visdom(
            server=config.VISDOM_SERVER,
            port=config.VISDOM_PORT,
            env=config.ENV_NAME
        )
        
        # Create reports directory if it doesn't exist
        Path("reports").mkdir(exist_ok=True)
    
    def matplot(self, fig: plt.Figure, win: Optional[str] = None, title: str = "") -> None:
        """Display matplotlib figure in Visdom.
        
        Args:
            fig: Matplotlib figure
            win: Window name
            title: Plot title
        """
        self.viz.matplot(fig, win=win, opts={"title": title})
    
    def plotlyplot(self, fig, win: Optional[str] = None) -> None:
        """Display plotly figure in Visdom.
        
        Args:
            fig: Plotly figure
            win: Window name
        """
        self.viz.plotlyplot(fig, win=win)
    
    def export(self, fig, name: str) -> None:
        """Save figure to reports directory.
        
        Args:
            fig: Plotly figure
            name: Filename without extension
        """
        if config.EXPORT_REPORTS:
            try:
                pio.write_image(fig, f"reports/{name}.png")
                fig.write_html(f"reports/{name}.html")
            except Exception as e:
                print(f"Warning: Could not export {name}: {e}")
    
    def text(self, text: str, win: Optional[str] = None, title: str = "") -> None:
        """Display text in Visdom.
        
        Args:
            text: Text content
            win: Window name
            title: Window title
        """
        self.viz.text(text, win=win, opts={"title": title})


# Global visualizer instance
viz = Visualizer()
