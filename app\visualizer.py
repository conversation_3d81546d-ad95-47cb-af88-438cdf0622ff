"""Visdom wrapper with matplotlib and plotly support."""

import visdom
import plotly.io as pio
import matplotlib.pyplot as plt
from pathlib import Path
from typing import Optional, Union
from . import config


class Visualizer:
    """Wrapper class for Visdom visualization with export functionality."""

    def __init__(self):
        """Initialize Visdom connection."""
        try:
            self.viz = visdom.Visdom(
                server=config.VISDOM_SERVER,
                port=config.VISDOM_PORT,
                env=config.ENV_NAME
            )
            self.visdom_available = self.viz.check_connection()
        except:
            self.viz = None
            self.visdom_available = False

        # Create reports directory if it doesn't exist
        Path("reports").mkdir(exist_ok=True)
    
    def matplot(self, fig: plt.Figure, win: Optional[str] = None, title: str = "") -> None:
        """Display matplotlib figure in Visdom.

        Args:
            fig: Matplotlib figure
            win: Window name
            title: Plot title
        """
        if self.visdom_available and self.viz:
            try:
                self.viz.matplot(fig, win=win, opts={"title": title})
            except:
                print(f"Warning: Failed to display plot '{title}' in Visdom")

    def plotlyplot(self, fig, win: Optional[str] = None) -> None:
        """Display plotly figure in Visdom.

        Args:
            fig: Plotly figure
            win: Window name
        """
        if self.visdom_available and self.viz:
            try:
                self.viz.plotlyplot(fig, win=win)
            except:
                print(f"Warning: Failed to display plot in Visdom")

        # Always try to export if enabled
        if config.EXPORT_REPORTS and win:
            self.export(fig, win)
    
    def export(self, fig, name: str) -> None:
        """Save figure to reports directory.
        
        Args:
            fig: Plotly figure
            name: Filename without extension
        """
        if config.EXPORT_REPORTS:
            try:
                pio.write_image(fig, f"reports/{name}.png")
                fig.write_html(f"reports/{name}.html")
            except Exception as e:
                print(f"Warning: Could not export {name}: {e}")
    
    def text(self, text: str, win: Optional[str] = None, title: str = "") -> None:
        """Display text in Visdom.

        Args:
            text: Text content
            win: Window name
            title: Window title
        """
        if self.visdom_available and self.viz:
            try:
                self.viz.text(text, win=win, opts={"title": title})
            except:
                print(f"Warning: Failed to display text '{title}' in Visdom")
        else:
            # Print summary to console if Visdom not available
            print(f"\n=== {title} ===")
            # Strip HTML tags for console output
            import re
            clean_text = re.sub('<[^<]+?>', '', text)
            print(clean_text)


# Global visualizer instance
viz = Visualizer()
