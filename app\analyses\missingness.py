"""Missing value analysis with visualization using missingno."""

import pandas as pd
import matplotlib.pyplot as plt
import missingno as msno
import plotly.express as px
import plotly.graph_objects as go
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ..visualizer import Visualizer


def run(df: pd.DataFrame, viz: "Visualizer") -> None:
    """Analyze missing values and create visualizations.
    
    Args:
        df: Input DataFrame
        viz: Visualizer instance
    """
    # Calculate missing value statistics
    missing_stats = df.isnull().sum()
    missing_pct = (missing_stats / len(df) * 100).round(2)
    
    # Create missing value summary
    missing_summary = pd.DataFrame({
        'Column': missing_stats.index,
        'Missing_Count': missing_stats.values,
        'Missing_Percentage': missing_pct.values
    }).sort_values('Missing_Count', ascending=False)
    
    # Filter columns with missing values
    missing_cols = missing_summary[missing_summary['Missing_Count'] > 0]
    
    if len(missing_cols) == 0:
        viz.text("No missing values found in the dataset!", 
                win="missing_summary", title="Missing Values Summary")
        return
    
    # Create bar chart of missing values
    fig_bar = px.bar(
        missing_cols.head(15),
        x='Column',
        y='Missing_Percentage',
        title='Missing Values by Column (Top 15)',
        labels={'Missing_Percentage': 'Missing %', 'Column': 'Columns'},
        color='Missing_Percentage',
        color_continuous_scale='Reds'
    )
    fig_bar.update_xaxis(tickangle=45)
    viz.plotlyplot(fig_bar, win="missing_bar")
    viz.export(fig_bar, "missing_values_bar")
    
    # Create missing value matrix using missingno
    plt.figure(figsize=(12, 8))
    msno.matrix(df.iloc[:, :20])  # Limit to first 20 columns for readability
    plt.title('Missing Value Matrix')
    plt.tight_layout()
    viz.matplot(plt.gcf(), win="missing_matrix", title="Missing Value Matrix")
    plt.close()
    
    # Create missing value heatmap
    plt.figure(figsize=(10, 8))
    msno.heatmap(df.iloc[:, :20])
    plt.title('Missing Value Correlation Heatmap')
    plt.tight_layout()
    viz.matplot(plt.gcf(), win="missing_heatmap", title="Missing Value Heatmap")
    plt.close()
    
    # Create summary text
    total_missing = missing_stats.sum()
    total_cells = df.shape[0] * df.shape[1]
    overall_missing_pct = (total_missing / total_cells * 100).round(2)
    
    summary_text = f"""
    <h3>Missing Values Summary</h3>
    <p><strong>Total missing values:</strong> {total_missing:,}</p>
    <p><strong>Overall missing percentage:</strong> {overall_missing_pct}%</p>
    <p><strong>Columns with missing values:</strong> {len(missing_cols)}/{len(df.columns)}</p>
    <p><strong>Most incomplete column:</strong> {missing_cols.iloc[0]['Column']} ({missing_cols.iloc[0]['Missing_Percentage']}%)</p>
    """
    
    viz.text(summary_text, win="missing_summary", title="Missing Values Summary")
