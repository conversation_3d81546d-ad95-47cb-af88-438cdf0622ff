"""Data preprocessing module for cleaning and feature engineering."""

import pandas as pd
import numpy as np
from typing import Optional


def clean(df: pd.DataFrame) -> pd.DataFrame:
    """Clean and preprocess the dataset.
    
    Args:
        df: Raw DataFrame
        
    Returns:
        pd.DataFrame: Cleaned and processed DataFrame
    """
    df_clean = df.copy()
    
    # Convert date columns if they exist
    date_columns = ['Publication_Date', 'Release_Date', 'Date']
    for col in date_columns:
        if col in df_clean.columns:
            df_clean[col] = pd.to_datetime(df_clean[col], errors='coerce')
    
    # Convert numeric columns
    numeric_columns = ['Rating', 'Price', 'Pages', 'Runtime', 'Year']
    for col in numeric_columns:
        if col in df_clean.columns:
            df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce')
    
    # Clean text columns - strip whitespace and handle empty strings
    text_columns = ['Title', 'Author', 'Genre', 'Description', 'Director', 'Cast']
    for col in text_columns:
        if col in df_clean.columns:
            df_clean[col] = df_clean[col].astype(str).str.strip()
            df_clean[col] = df_clean[col].replace(['', 'nan', 'None'], np.nan)
    
    # Add derived features
    if 'Description' in df_clean.columns:
        df_clean['Description_Length'] = df_clean['Description'].str.len()
    
    if 'Title' in df_clean.columns:
        df_clean['Title_Length'] = df_clean['Title'].str.len()
    
    # Add completeness score for each row
    df_clean['Row_Completeness'] = df_clean.notna().mean(axis=1) * 100
    
    return df_clean


def extract_features(df: pd.DataFrame) -> pd.DataFrame:
    """Extract additional features from the dataset.
    
    Args:
        df: Input DataFrame
        
    Returns:
        pd.DataFrame: DataFrame with additional features
    """
    df_features = df.copy()
    
    # Extract year from date columns if available
    date_cols = df_features.select_dtypes(include=['datetime64']).columns
    for col in date_cols:
        df_features[f'{col}_Year'] = df_features[col].dt.year
        df_features[f'{col}_Month'] = df_features[col].dt.month
    
    # Text analysis features
    text_cols = ['Title', 'Description', 'Genre']
    for col in text_cols:
        if col in df_features.columns:
            df_features[f'{col}_Word_Count'] = df_features[col].str.split().str.len()
    
    return df_features
