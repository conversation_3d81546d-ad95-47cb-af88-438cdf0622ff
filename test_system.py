#!/usr/bin/env python3
"""Simple system test to verify all components work."""

import sys
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """Test that all modules can be imported."""
    print("Testing imports...")
    
    try:
        from app import config, loader, preprocess, analysis_core
        print("✓ Core modules imported successfully")
    except Exception as e:
        print(f"✗ Core module import failed: {e}")
        return False
    
    try:
        from app.analyses import (
            missingness, completeness, failure_patterns, dup_detect,
            outlier_detect, field_corr, time_trend, cat_distribution,
            text_len, rule_check
        )
        print("✓ Analysis modules imported successfully")
    except Exception as e:
        print(f"✗ Analysis module import failed: {e}")
        return False
    
    return True

def test_data_loading():
    """Test data loading functionality."""
    print("\nTesting data loading...")
    
    try:
        from app import loader, config
        
        # Test with sample data
        df = loader.load_data(config.DATA_PATH)
        print(f"✓ Data loaded: {len(df)} rows, {len(df.columns)} columns")
        
        # Check expected columns
        expected_cols = ['Title', 'Author', 'Genre', 'Year', 'Rating']
        missing_cols = [col for col in expected_cols if col not in df.columns]
        if missing_cols:
            print(f"⚠ Missing expected columns: {missing_cols}")
        else:
            print("✓ All expected columns present")
        
        return True
    except Exception as e:
        print(f"✗ Data loading failed: {e}")
        return False

def test_preprocessing():
    """Test data preprocessing."""
    print("\nTesting preprocessing...")
    
    try:
        from app import loader, preprocess, config
        
        df = loader.load_data(config.DATA_PATH)
        df_clean = preprocess.clean(df)
        
        print(f"✓ Preprocessing completed: {len(df_clean)} rows")
        
        # Check for derived columns
        if 'Row_Completeness' in df_clean.columns:
            print("✓ Row completeness calculated")
        else:
            print("⚠ Row completeness not calculated")
        
        return True
    except Exception as e:
        print(f"✗ Preprocessing failed: {e}")
        return False

def test_analysis_core():
    """Test core analysis functions."""
    print("\nTesting analysis core functions...")
    
    try:
        import pandas as pd
        import numpy as np
        from app.analysis_core import field_completion, row_completion, iso_outliers
        
        # Create test data
        test_df = pd.DataFrame({
            'A': [1, 2, np.nan, 4],
            'B': [1, np.nan, np.nan, 4],
            'C': [1, 2, 3, 4]
        })
        
        # Test field completion
        field_comp = field_completion(test_df)
        print(f"✓ Field completion calculated: {field_comp['A']:.1f}% for column A")
        
        # Test row completion
        row_comp = row_completion(test_df)
        print(f"✓ Row completion calculated: {row_comp.iloc[0]:.1f}% for first row")
        
        # Test outlier detection
        test_series = pd.Series([1, 2, 3, 4, 100])  # 100 is an outlier
        outliers = iso_outliers(test_series)
        print(f"✓ Outlier detection completed: {outliers.sum()} outliers found")
        
        return True
    except Exception as e:
        print(f"✗ Analysis core test failed: {e}")
        return False

def main():
    """Run all system tests."""
    print("=" * 50)
    print("Data Quality Analysis System - Integration Test")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_data_loading,
        test_preprocessing,
        test_analysis_core
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! System is ready to use.")
        print("\nTo run the full analysis:")
        print("1. Start Visdom: python -m visdom.server --port 8097")
        print("2. Run analysis: python app/main.py")
        print("3. View results: http://localhost:8097")
    else:
        print("✗ Some tests failed. Please check the errors above.")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
