"""Field correlation analysis using phik for mixed data types."""

import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ..visualizer import Visualizer


def calculate_correlations(df: pd.DataFrame) -> pd.DataFrame:
    """Calculate correlation matrix using multiple methods.
    
    Args:
        df: Input DataFrame
        
    Returns:
        Correlation matrix
    """
    # Try to use phik for mixed data types
    try:
        import phik
        # Select columns with sufficient data
        valid_cols = []
        for col in df.columns:
            if df[col].notna().sum() > 10:  # At least 10 non-null values
                valid_cols.append(col)
        
        if len(valid_cols) < 2:
            return pd.DataFrame()
        
        df_subset = df[valid_cols].copy()
        
        # Calculate phik correlation matrix
        corr_matrix = df_subset.phik_matrix()
        return corr_matrix
        
    except ImportError:
        # Fallback to standard correlation for numeric columns only
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) < 2:
            return pd.DataFrame()
        
        return df[numeric_cols].corr()


def run(df: pd.DataFrame, viz: "Visualizer") -> None:
    """Analyze field correlations and create visualizations.
    
    Args:
        df: Input DataFrame
        viz: Visualizer instance
    """
    # Calculate correlation matrix
    corr_matrix = calculate_correlations(df)
    
    if corr_matrix.empty or len(corr_matrix) < 2:
        viz.text("Insufficient data for correlation analysis!", 
                win="correlation_summary", title="Field Correlation Summary")
        return
    
    # Create correlation heatmap
    fig_heatmap = px.imshow(
        corr_matrix,
        title='Field Correlation Matrix',
        color_continuous_scale='RdBu',
        aspect='auto',
        labels={'color': 'Correlation'},
        zmin=-1,
        zmax=1
    )
    
    # Add correlation values as text
    fig_heatmap.update_traces(
        text=np.round(corr_matrix.values, 2),
        texttemplate='%{text}',
        textfont={'size': 10}
    )
    
    fig_heatmap.update_layout(
        width=max(600, len(corr_matrix) * 50),
        height=max(600, len(corr_matrix) * 50)
    )
    
    viz.plotlyplot(fig_heatmap, win="correlation_heatmap")
    viz.export(fig_heatmap, "correlation_heatmap")
    
    # Find strong correlations (excluding self-correlations)
    strong_correlations = []
    
    for i in range(len(corr_matrix)):
        for j in range(i + 1, len(corr_matrix)):
            corr_value = corr_matrix.iloc[i, j]
            if not pd.isna(corr_value) and abs(corr_value) > 0.5:
                strong_correlations.append({
                    'Field1': corr_matrix.index[i],
                    'Field2': corr_matrix.columns[j],
                    'Correlation': corr_value,
                    'Abs_Correlation': abs(corr_value),
                    'Type': 'Positive' if corr_value > 0 else 'Negative'
                })
    
    if strong_correlations:
        strong_corr_df = pd.DataFrame(strong_correlations).sort_values(
            'Abs_Correlation', ascending=False
        )
        
        # Create bar chart of strong correlations
        fig_strong = px.bar(
            strong_corr_df.head(15),
            x='Abs_Correlation',
            y=[f"{row['Field1']} ↔ {row['Field2']}" for _, row in strong_corr_df.head(15).iterrows()],
            orientation='h',
            title='Strongest Field Correlations (|r| > 0.5)',
            labels={'Abs_Correlation': 'Absolute Correlation', 'y': 'Field Pairs'},
            color='Type',
            color_discrete_map={'Positive': 'green', 'Negative': 'red'}
        )
        fig_strong.update_layout(height=max(400, len(strong_corr_df.head(15)) * 30))
        viz.plotlyplot(fig_strong, win="strong_correlations")
        viz.export(fig_strong, "strong_correlations")
    
    # Create network-style visualization for top correlations
    if strong_correlations:
        top_correlations = strong_corr_df.head(10)
        
        # Create network graph data
        nodes = list(set(top_correlations['Field1'].tolist() + top_correlations['Field2'].tolist()))
        node_indices = {node: i for i, node in enumerate(nodes)}
        
        # Create edge traces
        edge_traces = []
        for _, row in top_correlations.iterrows():
            x0, y0 = node_indices[row['Field1']], 0
            x1, y1 = node_indices[row['Field2']], 1
            
            edge_traces.append(
                go.Scatter(
                    x=[x0, x1, None],
                    y=[y0, y1, None],
                    mode='lines',
                    line=dict(
                        width=abs(row['Correlation']) * 5,
                        color='red' if row['Correlation'] < 0 else 'green'
                    ),
                    hoverinfo='none',
                    showlegend=False
                )
            )
        
        # Create node traces
        node_trace = go.Scatter(
            x=list(range(len(nodes))) + list(range(len(nodes))),
            y=[0] * len(nodes) + [1] * len(nodes),
            mode='markers+text',
            marker=dict(size=20, color='lightblue'),
            text=nodes + nodes,
            textposition='middle center',
            hoverinfo='text',
            showlegend=False
        )
        
        fig_network = go.Figure(data=edge_traces + [node_trace])
        fig_network.update_layout(
            title='Correlation Network (Top 10 Correlations)',
            showlegend=False,
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            height=400
        )
        
        viz.plotlyplot(fig_network, win="correlation_network")
        viz.export(fig_network, "correlation_network")
    
    # Analyze correlation distribution
    # Get upper triangle of correlation matrix (excluding diagonal)
    upper_triangle = corr_matrix.where(
        np.triu(np.ones(corr_matrix.shape), k=1).astype(bool)
    )
    correlations_flat = upper_triangle.stack().dropna()
    
    if len(correlations_flat) > 0:
        fig_dist = px.histogram(
            x=correlations_flat,
            nbins=30,
            title='Distribution of All Pairwise Correlations',
            labels={'x': 'Correlation Coefficient', 'y': 'Frequency'},
            color_discrete_sequence=['skyblue']
        )
        fig_dist.add_vline(x=0, line_dash="dash", line_color="black")
        viz.plotlyplot(fig_dist, win="correlation_distribution")
        viz.export(fig_dist, "correlation_distribution")
    
    # Create summary statistics
    if len(correlations_flat) > 0:
        mean_corr = correlations_flat.mean()
        std_corr = correlations_flat.std()
        max_corr = correlations_flat.max()
        min_corr = correlations_flat.min()
        
        # Count strong correlations
        strong_positive = (correlations_flat > 0.5).sum()
        strong_negative = (correlations_flat < -0.5).sum()
        weak_correlations = (abs(correlations_flat) < 0.3).sum()
    
    # Create summary text
    summary_text = f"""
    <h3>Field Correlation Analysis Summary</h3>
    <p><strong>Fields analyzed:</strong> {len(corr_matrix)}</p>
    <p><strong>Total pairwise correlations:</strong> {len(correlations_flat) if len(correlations_flat) > 0 else 0}</p>
    """
    
    if len(correlations_flat) > 0:
        summary_text += f"""
        <h4>Correlation Statistics:</h4>
        <p><strong>Mean correlation:</strong> {mean_corr:.3f}</p>
        <p><strong>Standard deviation:</strong> {std_corr:.3f}</p>
        <p><strong>Range:</strong> {min_corr:.3f} to {max_corr:.3f}</p>
        
        <h4>Correlation Strength Distribution:</h4>
        <p><strong>Strong positive (r > 0.5):</strong> {strong_positive}</p>
        <p><strong>Strong negative (r < -0.5):</strong> {strong_negative}</p>
        <p><strong>Weak correlations (|r| < 0.3):</strong> {weak_correlations}</p>
        """
    
    if strong_correlations:
        summary_text += f"""
        <h4>Strongest Correlations:</h4>
        """
        for _, row in strong_corr_df.head(5).iterrows():
            summary_text += f"<p>• {row['Field1']} ↔ {row['Field2']}: {row['Correlation']:.3f}</p>"
    else:
        summary_text += "<p><strong>No strong correlations found (|r| > 0.5)</strong></p>"
    
    viz.text(summary_text, win="correlation_summary", title="Field Correlation Summary")
