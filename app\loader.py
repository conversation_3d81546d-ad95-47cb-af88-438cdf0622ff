"""Universal data loader supporting CSV, Excel, and JSON formats."""

import pandas as pd
import pathlib
from typing import Union


def load_data(path: Union[str, pathlib.Path]) -> pd.DataFrame:
    """Auto-detect file type and return DataFrame.
    
    Args:
        path: Path to the data file
        
    Returns:
        pd.DataFrame: Loaded data
        
    Raises:
        ValueError: If file type is not supported
    """
    path = pathlib.Path(path)
    
    if path.suffix == ".csv":
        return pd.read_csv(path)
    elif path.suffix in {".xls", ".xlsx"}:
        return pd.read_excel(path, sheet_name=0)
    elif path.suffix == ".json":
        return pd.read_json(path, orient="records")
    else:
        raise ValueError(f"Unsupported file type: {path.suffix}")
