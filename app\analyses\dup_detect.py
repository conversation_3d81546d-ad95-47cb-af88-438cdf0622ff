"""Duplicate detection based on Title+Author with visualization."""

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from typing import TYPE_CHECKING, List

if TYPE_CHECKING:
    from ..visualizer import Visualizer


def find_duplicates(df: pd.DataFrame, subset: List[str]) -> pd.DataFrame:
    """Find duplicate records based on specified columns.
    
    Args:
        df: Input DataFrame
        subset: List of column names to check for duplicates
        
    Returns:
        DataFrame containing duplicate records
    """
    # Filter out rows where any of the subset columns are null
    valid_rows = df[subset].notna().all(axis=1)
    df_valid = df[valid_rows].copy()
    
    if len(df_valid) == 0:
        return pd.DataFrame()
    
    # Find duplicates
    dup_mask = df_valid.duplicated(subset=subset, keep=False)
    return df_valid[dup_mask]


def run(df: pd.DataFrame, viz: "Visualizer") -> None:
    """Detect duplicates and create visualizations.
    
    Args:
        df: Input DataFrame
        viz: Visualizer instance
    """
    # Define potential duplicate detection strategies
    strategies = []
    
    # Strategy 1: Title + Author (for books)
    if 'Title' in df.columns and 'Author' in df.columns:
        strategies.append(('Title + Author', ['Title', 'Author']))
    
    # Strategy 2: Title only
    if 'Title' in df.columns:
        strategies.append(('Title Only', ['Title']))
    
    # Strategy 3: Title + Director (for movies)
    if 'Title' in df.columns and 'Director' in df.columns:
        strategies.append(('Title + Director', ['Title', 'Director']))
    
    # Strategy 4: ISBN (for books)
    if 'ISBN' in df.columns:
        strategies.append(('ISBN', ['ISBN']))
    
    # Strategy 5: IMDb ID (for movies)
    if 'IMDb_ID' in df.columns:
        strategies.append(('IMDb ID', ['IMDb_ID']))
    
    if not strategies:
        viz.text("No suitable columns found for duplicate detection!", 
                win="duplicate_summary", title="Duplicate Detection Summary")
        return
    
    # Analyze duplicates for each strategy
    duplicate_results = {}
    
    for strategy_name, columns in strategies:
        dup_df = find_duplicates(df, columns)
        if len(dup_df) > 0:
            # Count duplicates by group
            dup_counts = dup_df.groupby(columns).size().reset_index(name='Count')
            dup_counts = dup_counts.sort_values('Count', ascending=False)
            duplicate_results[strategy_name] = {
                'duplicates': dup_df,
                'counts': dup_counts,
                'total_duplicates': len(dup_df),
                'unique_groups': len(dup_counts)
            }
    
    if not duplicate_results:
        viz.text("No duplicates found with any strategy!", 
                win="duplicate_summary", title="Duplicate Detection Summary")
        return
    
    # Create visualization for the primary strategy (Title + Author if available)
    primary_strategy = None
    if 'Title + Author' in duplicate_results:
        primary_strategy = 'Title + Author'
    elif 'Title Only' in duplicate_results:
        primary_strategy = 'Title Only'
    else:
        primary_strategy = list(duplicate_results.keys())[0]
    
    primary_data = duplicate_results[primary_strategy]
    
    # Create bar chart of top duplicates
    top_duplicates = primary_data['counts'].head(15)
    
    # Create labels for the chart
    if primary_strategy == 'Title + Author':
        labels = [f"{row['Title']} - {row['Author']}" for _, row in top_duplicates.iterrows()]
    elif primary_strategy == 'Title + Director':
        labels = [f"{row['Title']} - {row['Director']}" for _, row in top_duplicates.iterrows()]
    else:
        labels = [str(row[top_duplicates.columns[0]]) for _, row in top_duplicates.iterrows()]
    
    fig_bar = px.bar(
        x=labels,
        y=top_duplicates['Count'],
        title=f'Top 15 Duplicates by {primary_strategy}',
        labels={'x': primary_strategy, 'y': 'Number of Duplicates'},
        color=top_duplicates['Count'],
        color_continuous_scale='Reds'
    )
    fig_bar.update_xaxis(tickangle=45)
    fig_bar.update_layout(height=600)
    viz.plotlyplot(fig_bar, win="duplicate_bar")
    viz.export(fig_bar, "duplicate_detection")
    
    # Create summary comparison chart
    strategy_summary = pd.DataFrame([
        {
            'Strategy': strategy,
            'Total_Duplicates': data['total_duplicates'],
            'Unique_Groups': data['unique_groups'],
            'Avg_Group_Size': data['total_duplicates'] / data['unique_groups']
        }
        for strategy, data in duplicate_results.items()
    ])
    
    fig_comparison = px.bar(
        strategy_summary,
        x='Strategy',
        y='Total_Duplicates',
        title='Duplicate Detection Results by Strategy',
        labels={'Total_Duplicates': 'Total Duplicate Records', 'Strategy': 'Detection Strategy'},
        color='Total_Duplicates',
        color_continuous_scale='Blues'
    )
    viz.plotlyplot(fig_comparison, win="strategy_comparison")
    viz.export(fig_comparison, "duplicate_strategies")
    
    # Create distribution of duplicate group sizes
    if primary_strategy in duplicate_results:
        group_sizes = duplicate_results[primary_strategy]['counts']['Count']
        fig_dist = px.histogram(
            x=group_sizes,
            nbins=min(20, group_sizes.max()),
            title=f'Distribution of Duplicate Group Sizes ({primary_strategy})',
            labels={'x': 'Group Size', 'y': 'Number of Groups'},
            color_discrete_sequence=['lightcoral']
        )
        viz.plotlyplot(fig_dist, win="group_size_dist")
        viz.export(fig_dist, "duplicate_group_sizes")
    
    # Create summary text
    total_records = len(df)
    summary_text = f"""
    <h3>Duplicate Detection Summary</h3>
    <p><strong>Total records analyzed:</strong> {total_records:,}</p>
    
    <h4>Results by Strategy:</h4>
    """
    
    for strategy, data in duplicate_results.items():
        pct = (data['total_duplicates'] / total_records * 100)
        summary_text += f"""
        <p><strong>{strategy}:</strong></p>
        <ul>
            <li>Duplicate records: {data['total_duplicates']:,} ({pct:.1f}%)</li>
            <li>Unique duplicate groups: {data['unique_groups']:,}</li>
            <li>Average group size: {data['avg_group_size']:.1f}</li>
        </ul>
        """
    
    if primary_strategy in duplicate_results:
        largest_group = duplicate_results[primary_strategy]['counts'].iloc[0]
        summary_text += f"""
        <h4>Largest Duplicate Group ({primary_strategy}):</h4>
        <p>{largest_group['Count']} copies of: {largest_group.iloc[0] if len(largest_group) > 2 else 'N/A'}</p>
        """
    
    viz.text(summary_text, win="duplicate_summary", title="Duplicate Detection Summary")
