"""Time trend analysis for temporal patterns."""

import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from typing import TYPE_CHECKING, List

if TYPE_CHECKING:
    from ..visualizer import Visualizer


def find_date_columns(df: pd.DataFrame) -> List[str]:
    """Find columns that contain date/time information.
    
    Args:
        df: Input DataFrame
        
    Returns:
        List of column names containing dates
    """
    date_cols = []
    
    # Check for datetime columns
    datetime_cols = df.select_dtypes(include=['datetime64']).columns.tolist()
    date_cols.extend(datetime_cols)
    
    # Check for potential date columns by name
    potential_date_names = ['date', 'year', 'publication_date', 'release_date', 
                           'created_at', 'updated_at', 'timestamp']
    
    for col in df.columns:
        if any(date_name in col.lower() for date_name in potential_date_names):
            if col not in date_cols:
                date_cols.append(col)
    
    return date_cols


def analyze_temporal_patterns(df: pd.DataFrame, date_col: str) -> dict:
    """Analyze temporal patterns in a date column.
    
    Args:
        df: Input DataFrame
        date_col: Name of the date column
        
    Returns:
        Dictionary with analysis results
    """
    results = {}
    
    # Convert to datetime if not already
    if not pd.api.types.is_datetime64_any_dtype(df[date_col]):
        date_series = pd.to_datetime(df[date_col], errors='coerce')
    else:
        date_series = df[date_col]
    
    # Remove null dates
    valid_dates = date_series.dropna()
    
    if len(valid_dates) == 0:
        return {'error': 'No valid dates found'}
    
    results['valid_count'] = len(valid_dates)
    results['null_count'] = len(date_series) - len(valid_dates)
    results['date_range'] = (valid_dates.min(), valid_dates.max())
    
    # Extract time components
    results['years'] = valid_dates.dt.year
    results['months'] = valid_dates.dt.month
    results['days_of_week'] = valid_dates.dt.day_name()
    results['days_of_month'] = valid_dates.dt.day
    
    # Calculate trends
    yearly_counts = results['years'].value_counts().sort_index()
    monthly_counts = results['months'].value_counts().sort_index()
    
    results['yearly_trend'] = yearly_counts
    results['monthly_pattern'] = monthly_counts
    results['weekday_pattern'] = results['days_of_week'].value_counts()
    
    return results


def run(df: pd.DataFrame, viz: "Visualizer") -> None:
    """Analyze time trends and create visualizations.
    
    Args:
        df: Input DataFrame
        viz: Visualizer instance
    """
    # Find date columns
    date_columns = find_date_columns(df)
    
    if not date_columns:
        viz.text("No date/time columns found for temporal analysis!", 
                win="time_trend_summary", title="Time Trend Analysis Summary")
        return
    
    # Analyze each date column
    analysis_results = {}
    
    for col in date_columns:
        if col in df.columns:
            results = analyze_temporal_patterns(df, col)
            if 'error' not in results:
                analysis_results[col] = results
    
    if not analysis_results:
        viz.text("No valid temporal data found for analysis!", 
                win="time_trend_summary", title="Time Trend Analysis Summary")
        return
    
    # Create visualizations for the primary date column
    primary_col = list(analysis_results.keys())[0]
    primary_data = analysis_results[primary_col]
    
    # 1. Yearly trend
    if len(primary_data['yearly_trend']) > 1:
        yearly_df = pd.DataFrame({
            'Year': primary_data['yearly_trend'].index,
            'Count': primary_data['yearly_trend'].values
        })
        
        fig_yearly = px.line(
            yearly_df,
            x='Year',
            y='Count',
            title=f'Yearly Trend - {primary_col}',
            labels={'Count': 'Number of Records', 'Year': 'Year'},
            markers=True
        )
        fig_yearly.update_traces(line_color='blue', marker_size=8)
        viz.plotlyplot(fig_yearly, win="yearly_trend")
        viz.export(fig_yearly, "yearly_trend")
    
    # 2. Monthly pattern
    if len(primary_data['monthly_pattern']) > 1:
        month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        
        monthly_df = pd.DataFrame({
            'Month': [month_names[i-1] for i in primary_data['monthly_pattern'].index],
            'Count': primary_data['monthly_pattern'].values,
            'Month_Num': primary_data['monthly_pattern'].index
        }).sort_values('Month_Num')
        
        fig_monthly = px.bar(
            monthly_df,
            x='Month',
            y='Count',
            title=f'Monthly Distribution - {primary_col}',
            labels={'Count': 'Number of Records', 'Month': 'Month'},
            color='Count',
            color_continuous_scale='Blues'
        )
        viz.plotlyplot(fig_monthly, win="monthly_pattern")
        viz.export(fig_monthly, "monthly_pattern")
    
    # 3. Day of week pattern
    if len(primary_data['weekday_pattern']) > 1:
        weekday_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 
                        'Friday', 'Saturday', 'Sunday']
        
        weekday_df = pd.DataFrame({
            'Day': primary_data['weekday_pattern'].index,
            'Count': primary_data['weekday_pattern'].values
        })
        
        # Reorder by weekday
        weekday_df['Day_Order'] = weekday_df['Day'].map(
            {day: i for i, day in enumerate(weekday_order)}
        )
        weekday_df = weekday_df.sort_values('Day_Order')
        
        fig_weekday = px.bar(
            weekday_df,
            x='Day',
            y='Count',
            title=f'Day of Week Distribution - {primary_col}',
            labels={'Count': 'Number of Records', 'Day': 'Day of Week'},
            color='Count',
            color_continuous_scale='Greens'
        )
        viz.plotlyplot(fig_weekday, win="weekday_pattern")
        viz.export(fig_weekday, "weekday_pattern")
    
    # 4. Time series decomposition (if enough data)
    if len(primary_data['yearly_trend']) >= 3:
        # Create a simple trend analysis
        years = primary_data['yearly_trend'].index
        counts = primary_data['yearly_trend'].values
        
        # Calculate year-over-year growth
        if len(years) > 1:
            growth_rates = []
            growth_years = []
            
            for i in range(1, len(counts)):
                if counts[i-1] > 0:
                    growth_rate = ((counts[i] - counts[i-1]) / counts[i-1]) * 100
                    growth_rates.append(growth_rate)
                    growth_years.append(years[i])
            
            if growth_rates:
                growth_df = pd.DataFrame({
                    'Year': growth_years,
                    'Growth_Rate': growth_rates
                })
                
                fig_growth = px.bar(
                    growth_df,
                    x='Year',
                    y='Growth_Rate',
                    title=f'Year-over-Year Growth Rate - {primary_col}',
                    labels={'Growth_Rate': 'Growth Rate (%)', 'Year': 'Year'},
                    color='Growth_Rate',
                    color_continuous_scale='RdYlGn'
                )
                fig_growth.add_hline(y=0, line_dash="dash", line_color="black")
                viz.plotlyplot(fig_growth, win="growth_trend")
                viz.export(fig_growth, "growth_trend")
    
    # 5. Data quality over time
    if len(primary_data['yearly_trend']) > 1:
        # Analyze completeness by year
        df_with_year = df.copy()
        if not pd.api.types.is_datetime64_any_dtype(df_with_year[primary_col]):
            df_with_year[primary_col] = pd.to_datetime(df_with_year[primary_col], errors='coerce')
        
        df_with_year['Year'] = df_with_year[primary_col].dt.year
        
        # Calculate completeness by year
        yearly_completeness = df_with_year.groupby('Year').apply(
            lambda x: x.notna().mean().mean() * 100
        ).dropna()
        
        if len(yearly_completeness) > 1:
            completeness_df = pd.DataFrame({
                'Year': yearly_completeness.index,
                'Completeness': yearly_completeness.values
            })
            
            fig_quality = px.line(
                completeness_df,
                x='Year',
                y='Completeness',
                title=f'Data Completeness Over Time',
                labels={'Completeness': 'Completeness (%)', 'Year': 'Year'},
                markers=True
            )
            fig_quality.update_traces(line_color='green', marker_size=8)
            fig_quality.update_yaxis(range=[0, 100])
            viz.plotlyplot(fig_quality, win="quality_trend")
            viz.export(fig_quality, "quality_over_time")
    
    # Create summary text
    summary_text = f"""
    <h3>Time Trend Analysis Summary</h3>
    <p><strong>Date columns analyzed:</strong> {len(analysis_results)}</p>
    
    <h4>Primary Analysis: {primary_col}</h4>
    <p><strong>Valid dates:</strong> {primary_data['valid_count']:,}</p>
    <p><strong>Missing dates:</strong> {primary_data['null_count']:,}</p>
    <p><strong>Date range:</strong> {primary_data['date_range'][0].strftime('%Y-%m-%d')} to {primary_data['date_range'][1].strftime('%Y-%m-%d')}</p>
    <p><strong>Time span:</strong> {(primary_data['date_range'][1] - primary_data['date_range'][0]).days} days</p>
    """
    
    # Add yearly trend insights
    if len(primary_data['yearly_trend']) > 1:
        peak_year = primary_data['yearly_trend'].idxmax()
        peak_count = primary_data['yearly_trend'].max()
        total_records = primary_data['yearly_trend'].sum()
        
        summary_text += f"""
        <h4>Yearly Trends:</h4>
        <p><strong>Peak year:</strong> {peak_year} ({peak_count} records)</p>
        <p><strong>Total records:</strong> {total_records:,}</p>
        <p><strong>Average per year:</strong> {total_records / len(primary_data['yearly_trend']):.1f}</p>
        """
    
    # Add monthly insights
    if len(primary_data['monthly_pattern']) > 1:
        peak_month = primary_data['monthly_pattern'].idxmax()
        month_names = ['', 'January', 'February', 'March', 'April', 'May', 'June',
                      'July', 'August', 'September', 'October', 'November', 'December']
        
        summary_text += f"""
        <h4>Seasonal Patterns:</h4>
        <p><strong>Peak month:</strong> {month_names[peak_month]} ({primary_data['monthly_pattern'][peak_month]} records)</p>
        """
    
    # Add insights for other date columns
    if len(analysis_results) > 1:
        summary_text += f"""
        <h4>Other Date Columns:</h4>
        """
        for col, data in list(analysis_results.items())[1:]:
            summary_text += f"<p>• {col}: {data['valid_count']:,} valid dates</p>"
    
    viz.text(summary_text, win="time_trend_summary", title="Time Trend Analysis Summary")
