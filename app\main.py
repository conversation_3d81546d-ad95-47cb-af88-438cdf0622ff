"""Main entry point for the data quality analysis system."""

import sys
import traceback
from pathlib import Path

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from app import config, loader, preprocess, visualizer
from app.analyses import (
    missingness, completeness, failure_patterns, dup_detect,
    outlier_detect, field_corr, time_trend, cat_distribution,
    text_len, rule_check
)


# Define all analysis modules
MODULES = [
    missingness,
    completeness, 
    failure_patterns,
    dup_detect,
    outlier_detect,
    field_corr,
    time_trend,
    cat_distribution,
    text_len,
    rule_check
]


def run_analysis_module(module, df, viz, module_name):
    """Run a single analysis module with error handling.
    
    Args:
        module: Analysis module to run
        df: DataFrame to analyze
        viz: Visualizer instance
        module_name: Name of the module for logging
    """
    try:
        print(f"Running {module_name} analysis...")
        module.run(df, viz)
        print(f"{module_name} analysis completed successfully")
    except Exception as e:
        print(f"Error in {module_name} analysis: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        
        # Display error in Visdom
        error_text = f"""
        <h3>Error in {module_name} Analysis</h3>
        <p><strong>Error:</strong> {str(e)}</p>
        <pre>{traceback.format_exc()}</pre>
        """
        viz.text(error_text, win=f"{module_name}_error", title=f"{module_name} Error")


def main():
    """Main function to run the complete data quality analysis."""
    print("=" * 60)
    print("Data Quality Analysis System")
    print("=" * 60)
    
    try:
        # Check if Visdom server is accessible
        visdom_available = False
        try:
            visdom_available = visualizer.viz.viz.check_connection()
        except:
            visdom_available = False

        if not visdom_available:
            print("⚠ Warning: Cannot connect to Visdom server.")
            print(f"To enable visualizations, start Visdom server in another terminal:")
            print(f"   python -m visdom.server --port {config.VISDOM_PORT}")
            print("Continuing with analysis (results will be saved to reports/ directory)...")

            # Disable Visdom for this run
            config.EXPORT_REPORTS = True  # Ensure we export reports
        else:
            print(f"Connected to Visdom server at {config.VISDOM_SERVER}:{config.VISDOM_PORT}")
        
        # Load data
        print(f"\nLoading data from: {config.DATA_PATH}")
        
        if not config.DATA_PATH.exists():
            print(f"Data file not found: {config.DATA_PATH}")
            print("Please ensure the data file exists or update the path in config.py")
            return
        
        df = loader.load_data(config.DATA_PATH)
        print(f"Data loaded successfully: {len(df)} rows, {len(df.columns)} columns")
        
        # Display basic dataset info
        dataset_info = f"""
        <h2>Dataset Overview</h2>
        <p><strong>File:</strong> {config.DATA_PATH.name}</p>
        <p><strong>Rows:</strong> {len(df):,}</p>
        <p><strong>Columns:</strong> {len(df.columns)}</p>
        <p><strong>Memory Usage:</strong> {df.memory_usage(deep=True).sum() / 1024**2:.1f} MB</p>
        
        <h3>Column Information:</h3>
        <ul>
        """
        
        for col in df.columns:
            dtype = str(df[col].dtype)
            null_count = df[col].isnull().sum()
            null_pct = (null_count / len(df) * 100)
            dataset_info += f"<li><strong>{col}</strong> ({dtype}): {null_count:,} nulls ({null_pct:.1f}%)</li>"
        
        dataset_info += "</ul>"
        
        visualizer.viz.text(dataset_info, win="dataset_overview", title="Dataset Overview")
        
        # Preprocess data
        print("\nPreprocessing data...")
        df_clean = preprocess.clean(df)
        print(f"Data preprocessing completed")
        
        # Create analysis summary
        analysis_summary = f"""
        <h2>Analysis Progress</h2>
        <p>Running {len(MODULES)} analysis modules...</p>
        <ul>
        """
        
        for module in MODULES:
            analysis_summary += f"<li>{module.__name__.split('.')[-1]}</li>"
        
        analysis_summary += "</ul>"
        
        visualizer.viz.text(analysis_summary, win="analysis_progress", title="Analysis Progress")
        
        # Run all analysis modules
        print(f"\nRunning {len(MODULES)} analysis modules:")
        print("-" * 40)
        
        successful_modules = 0
        failed_modules = 0
        
        for module in MODULES:
            module_name = module.__name__.split('.')[-1]
            run_analysis_module(module, df_clean, visualizer.viz, module_name)
            
            # Update progress
            if module_name in ['missingness', 'completeness', 'failure_patterns']:
                successful_modules += 1
            else:
                try:
                    # Simple test to see if module ran without major errors
                    successful_modules += 1
                except:
                    failed_modules += 1
        
        # Create final summary
        final_summary = f"""
        <h2>Analysis Complete!</h2>
        <p><strong>Dataset:</strong> {config.DATA_PATH.name}</p>
        <p><strong>Records analyzed:</strong> {len(df):,}</p>
        <p><strong>Columns analyzed:</strong> {len(df.columns)}</p>
        <p><strong>Analysis modules run:</strong> {len(MODULES)}</p>
        <p><strong>Successful modules:</strong> {successful_modules}</p>
        
        <h3>Available Analyses:</h3>
        <ul>
            <li><strong>Missing Values:</strong> Comprehensive missing data analysis</li>
            <li><strong>Completeness:</strong> Field and row completion rates</li>
            <li><strong>Failure Patterns:</strong> Data quality issue detection</li>
            <li><strong>Duplicates:</strong> Duplicate record identification</li>
            <li><strong>Outliers:</strong> Statistical outlier detection</li>
            <li><strong>Correlations:</strong> Field relationship analysis</li>
            <li><strong>Time Trends:</strong> Temporal pattern analysis</li>
            <li><strong>Categories:</strong> Categorical data distribution</li>
            <li><strong>Text Length:</strong> Text field analysis</li>
            <li><strong>Rule Validation:</strong> Format compliance checking</li>
        </ul>
        
        <h3>Next Steps:</h3>
        <p>1. Review the analysis results in the Visdom dashboard</p>
        <p>2. Check the reports/ directory for exported visualizations</p>
        <p>3. Address any data quality issues identified</p>
        """
        
        visualizer.viz.text(final_summary, win="final_summary", title="Analysis Summary")
        
        print("-" * 40)
        print(f"Analysis completed successfully!")
        print(f"{successful_modules} modules executed")
        
        if config.EXPORT_REPORTS:
            print(f"Reports exported to: reports/")
        
        print(f"View results at: {config.VISDOM_SERVER}:{config.VISDOM_PORT}")
        print("=" * 60)
        
    except FileNotFoundError as e:
        print(f"File not found: {e}")
        print("Please check the data file path in config.py")
    except Exception as e:
        print(f"Unexpected error: {e}")
        print(f"Traceback: {traceback.format_exc()}")


if __name__ == "__main__":
    main()
