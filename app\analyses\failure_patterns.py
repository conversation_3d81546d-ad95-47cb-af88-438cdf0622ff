"""Failure pattern detection and analysis."""

import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from typing import TYPE_CHECKING, Dict, List

if TYPE_CHECKING:
    from ..visualizer import Visualizer


def detect_data_quality_issues(df: pd.DataFrame) -> Dict[str, pd.Series]:
    """Detect various data quality issues.
    
    Args:
        df: Input DataFrame
        
    Returns:
        Dict containing different types of issues
    """
    issues = {}
    
    # Detect empty strings
    for col in df.select_dtypes(include=['object']).columns:
        empty_strings = df[col].astype(str).str.strip() == ''
        if empty_strings.any():
            issues[f'{col}_empty_strings'] = empty_strings
    
    # Detect placeholder values
    placeholder_patterns = ['N/A', 'n/a', 'NULL', 'null', 'None', 'none', 
                           'Unknown', 'unknown', 'TBD', 'tbd', '???', 'XXX']
    
    for col in df.select_dtypes(include=['object']).columns:
        for pattern in placeholder_patterns:
            placeholder_mask = df[col].astype(str).str.contains(pattern, na=False)
            if placeholder_mask.any():
                issues[f'{col}_placeholder_{pattern}'] = placeholder_mask
    
    # Detect negative values in typically positive fields
    positive_fields = ['Rating', 'Price', 'Pages', 'Runtime', 'Year']
    for col in positive_fields:
        if col in df.columns and pd.api.types.is_numeric_dtype(df[col]):
            negative_mask = df[col] < 0
            if negative_mask.any():
                issues[f'{col}_negative_values'] = negative_mask
    
    # Detect unrealistic years
    if 'Year' in df.columns:
        current_year = pd.Timestamp.now().year
        unrealistic_years = (df['Year'] < 1800) | (df['Year'] > current_year + 5)
        if unrealistic_years.any():
            issues['Year_unrealistic'] = unrealistic_years
    
    # Detect extremely long text fields (potential data corruption)
    text_fields = ['Title', 'Description', 'Author']
    for col in text_fields:
        if col in df.columns:
            text_lengths = df[col].astype(str).str.len()
            extremely_long = text_lengths > text_lengths.quantile(0.99) * 2
            if extremely_long.any():
                issues[f'{col}_extremely_long'] = extremely_long
    
    return issues


def run(df: pd.DataFrame, viz: "Visualizer") -> None:
    """Analyze failure patterns and create visualizations.
    
    Args:
        df: Input DataFrame
        viz: Visualizer instance
    """
    # Detect data quality issues
    issues = detect_data_quality_issues(df)
    
    if not issues:
        viz.text("No data quality issues detected!", 
                win="failure_summary", title="Failure Patterns Summary")
        return
    
    # Count issues by type
    issue_counts = {}
    for issue_name, issue_mask in issues.items():
        issue_counts[issue_name] = issue_mask.sum()
    
    # Create DataFrame for visualization
    issues_df = pd.DataFrame([
        {'Issue_Type': issue_name, 'Count': count, 'Percentage': (count/len(df)*100)}
        for issue_name, count in issue_counts.items()
        if count > 0
    ]).sort_values('Count', ascending=False)
    
    if len(issues_df) == 0:
        viz.text("No significant data quality issues found!", 
                win="failure_summary", title="Failure Patterns Summary")
        return
    
    # Create bar chart of issues
    fig_issues = px.bar(
        issues_df.head(15),
        x='Issue_Type',
        y='Count',
        title='Data Quality Issues by Type (Top 15)',
        labels={'Count': 'Number of Records', 'Issue_Type': 'Issue Type'},
        color='Count',
        color_continuous_scale='Reds'
    )
    fig_issues.update_xaxis(tickangle=45)
    viz.plotlyplot(fig_issues, win="failure_patterns")
    viz.export(fig_issues, "failure_patterns")
    
    # Create percentage chart
    fig_pct = px.bar(
        issues_df.head(10),
        x='Issue_Type',
        y='Percentage',
        title='Data Quality Issues by Percentage (Top 10)',
        labels={'Percentage': 'Percentage of Records (%)', 'Issue_Type': 'Issue Type'},
        color='Percentage',
        color_continuous_scale='Oranges'
    )
    fig_pct.update_xaxis(tickangle=45)
    viz.plotlyplot(fig_pct, win="failure_percentages")
    viz.export(fig_pct, "failure_percentages")
    
    # Analyze patterns by row
    row_issue_counts = pd.Series(0, index=df.index)
    for issue_mask in issues.values():
        row_issue_counts += issue_mask.astype(int)
    
    # Create histogram of issues per row
    fig_row_issues = px.histogram(
        x=row_issue_counts,
        nbins=min(20, row_issue_counts.max() + 1),
        title='Distribution of Issues per Row',
        labels={'x': 'Number of Issues per Row', 'y': 'Number of Rows'},
        color_discrete_sequence=['coral']
    )
    viz.plotlyplot(fig_row_issues, win="issues_per_row")
    viz.export(fig_row_issues, "issues_per_row")
    
    # Calculate statistics
    total_issues = sum(issue_counts.values())
    affected_rows = (row_issue_counts > 0).sum()
    most_problematic_rows = row_issue_counts.nlargest(5)
    
    # Create summary text
    summary_text = f"""
    <h3>Failure Patterns Summary</h3>
    <p><strong>Total issues detected:</strong> {total_issues:,}</p>
    <p><strong>Issue types found:</strong> {len(issues_df)}</p>
    <p><strong>Affected rows:</strong> {affected_rows:,} ({affected_rows/len(df)*100:.1f}%)</p>
    <p><strong>Average issues per affected row:</strong> {total_issues/max(affected_rows, 1):.1f}</p>
    
    <h4>Most Common Issues:</h4>
    """
    
    for _, row in issues_df.head(5).iterrows():
        summary_text += f"<p>• {row['Issue_Type']}: {row['Count']} records ({row['Percentage']:.1f}%)</p>"
    
    if len(most_problematic_rows) > 0:
        summary_text += f"""
        <h4>Most Problematic Rows:</h4>
        <p>Row {most_problematic_rows.index[0]} has {most_problematic_rows.iloc[0]} issues</p>
        """
    
    viz.text(summary_text, win="failure_summary", title="Failure Patterns Summary")
