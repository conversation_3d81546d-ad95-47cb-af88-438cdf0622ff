version: "3.8"

services:
  data-quality-analysis:
    image: python:3.11-slim
    ports:
      - "8097:8097"
    volumes:
      - ./app:/app
      - ./data:/data
      - ./reports:/reports
      - ./requirements.txt:/requirements.txt
    working_dir: /
    environment:
      - ENV_NAME=quality_dashboard
      - PYTHONPATH=/
    command: >
      bash -c "
      pip install --no-cache-dir -r requirements.txt &&
      echo 'Starting Visdom server...' &&
      python -m visdom.server --port 8097 --env_path /tmp/visdom &
      sleep 5 &&
      echo 'Running data quality analysis...' &&
      python /app/main.py &&
      echo 'Analysis complete. Keeping Visdom server running...' &&
      wait"
    restart: unless-stopped

  # Alternative service for development - just runs Visdom server
  visdom-only:
    image: python:3.11-slim
    ports:
      - "8098:8097"
    volumes:
      - ./reports:/reports
    command: >
      bash -c "
      pip install --no-cache-dir visdom &&
      python -m visdom.server --port 8097 --env_path /tmp/visdom"
    restart: unless-stopped
    profiles:
      - dev
