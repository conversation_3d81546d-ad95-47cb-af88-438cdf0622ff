"""Rule validation for ISBN and IMDb ID format compliance."""

import pandas as pd
import numpy as np
import re
import plotly.express as px
import plotly.graph_objects as go
from typing import TYPE_CHECKING, Dict, List, Tuple

if TYPE_CHECKING:
    from ..visualizer import Visualizer


def validate_isbn(isbn_series: pd.Series) -> Dict:
    """Validate ISBN format compliance.
    
    Args:
        isbn_series: Series containing ISBN values
        
    Returns:
        Dictionary with validation results
    """
    # Clean ISBN values
    clean_isbns = isbn_series.astype(str).str.replace('-', '').str.replace(' ', '').str.upper()
    
    # Remove null/empty values
    valid_mask = (isbn_series.notna()) & (clean_isbns != 'NAN') & (clean_isbns.str.strip() != '')
    valid_isbns = clean_isbns[valid_mask]
    
    if len(valid_isbns) == 0:
        return {'error': 'No valid ISBN data'}
    
    # ISBN-10 pattern: 10 digits, last can be X
    isbn10_pattern = re.compile(r'^[0-9]{9}[0-9X]$')
    
    # ISBN-13 pattern: 13 digits
    isbn13_pattern = re.compile(r'^[0-9]{13}$')
    
    # Validate formats
    isbn10_valid = valid_isbns.str.match(isbn10_pattern)
    isbn13_valid = valid_isbns.str.match(isbn13_pattern)
    
    # Overall validity
    valid_format = isbn10_valid | isbn13_valid
    
    results = {
        'total_count': len(isbn_series),
        'non_empty_count': len(valid_isbns),
        'empty_count': len(isbn_series) - len(valid_isbns),
        'isbn10_count': isbn10_valid.sum(),
        'isbn13_count': isbn13_valid.sum(),
        'valid_format_count': valid_format.sum(),
        'invalid_format_count': (~valid_format).sum(),
        'valid_format_rate': valid_format.mean() * 100,
        'invalid_examples': valid_isbns[~valid_format].head(10).tolist()
    }
    
    return results


def validate_imdb_id(imdb_series: pd.Series) -> Dict:
    """Validate IMDb ID format compliance.
    
    Args:
        imdb_series: Series containing IMDb ID values
        
    Returns:
        Dictionary with validation results
    """
    # Clean IMDb IDs
    clean_imdb = imdb_series.astype(str).str.strip()
    
    # Remove null/empty values
    valid_mask = (imdb_series.notna()) & (clean_imdb != 'nan') & (clean_imdb.str.strip() != '')
    valid_imdb = clean_imdb[valid_mask]
    
    if len(valid_imdb) == 0:
        return {'error': 'No valid IMDb ID data'}
    
    # IMDb ID pattern: tt followed by 7+ digits
    imdb_pattern = re.compile(r'^tt[0-9]{7,}$')
    
    # Validate format
    valid_format = valid_imdb.str.match(imdb_pattern)
    
    results = {
        'total_count': len(imdb_series),
        'non_empty_count': len(valid_imdb),
        'empty_count': len(imdb_series) - len(valid_imdb),
        'valid_format_count': valid_format.sum(),
        'invalid_format_count': (~valid_format).sum(),
        'valid_format_rate': valid_format.mean() * 100,
        'invalid_examples': valid_imdb[~valid_format].head(10).tolist()
    }
    
    return results


def validate_email(email_series: pd.Series) -> Dict:
    """Validate email format compliance.
    
    Args:
        email_series: Series containing email values
        
    Returns:
        Dictionary with validation results
    """
    # Clean emails
    clean_emails = email_series.astype(str).str.strip().str.lower()
    
    # Remove null/empty values
    valid_mask = (email_series.notna()) & (clean_emails != 'nan') & (clean_emails.str.strip() != '')
    valid_emails = clean_emails[valid_mask]
    
    if len(valid_emails) == 0:
        return {'error': 'No valid email data'}
    
    # Basic email pattern
    email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    
    # Validate format
    valid_format = valid_emails.str.match(email_pattern)
    
    results = {
        'total_count': len(email_series),
        'non_empty_count': len(valid_emails),
        'empty_count': len(email_series) - len(valid_emails),
        'valid_format_count': valid_format.sum(),
        'invalid_format_count': (~valid_format).sum(),
        'valid_format_rate': valid_format.mean() * 100,
        'invalid_examples': valid_emails[~valid_format].head(10).tolist()
    }
    
    return results


def validate_url(url_series: pd.Series) -> Dict:
    """Validate URL format compliance.
    
    Args:
        url_series: Series containing URL values
        
    Returns:
        Dictionary with validation results
    """
    # Clean URLs
    clean_urls = url_series.astype(str).str.strip()
    
    # Remove null/empty values
    valid_mask = (url_series.notna()) & (clean_urls != 'nan') & (clean_urls.str.strip() != '')
    valid_urls = clean_urls[valid_mask]
    
    if len(valid_urls) == 0:
        return {'error': 'No valid URL data'}
    
    # Basic URL pattern
    url_pattern = re.compile(r'^https?://[^\s/$.?#].[^\s]*$', re.IGNORECASE)
    
    # Validate format
    valid_format = valid_urls.str.match(url_pattern)
    
    results = {
        'total_count': len(url_series),
        'non_empty_count': len(valid_urls),
        'empty_count': len(url_series) - len(valid_urls),
        'valid_format_count': valid_format.sum(),
        'invalid_format_count': (~valid_format).sum(),
        'valid_format_rate': valid_format.mean() * 100,
        'invalid_examples': valid_urls[~valid_format].head(10).tolist()
    }
    
    return results


def run(df: pd.DataFrame, viz: "Visualizer") -> None:
    """Validate rule compliance and create visualizations.
    
    Args:
        df: Input DataFrame
        viz: Visualizer instance
    """
    # Define validation rules and their corresponding columns
    validation_rules = []
    
    # ISBN validation
    isbn_columns = [col for col in df.columns if 'isbn' in col.lower()]
    for col in isbn_columns:
        validation_rules.append((col, 'ISBN', validate_isbn))
    
    # IMDb ID validation
    imdb_columns = [col for col in df.columns if 'imdb' in col.lower() or 'movie_id' in col.lower()]
    for col in imdb_columns:
        validation_rules.append((col, 'IMDb ID', validate_imdb_id))
    
    # Email validation
    email_columns = [col for col in df.columns if 'email' in col.lower() or 'mail' in col.lower()]
    for col in email_columns:
        validation_rules.append((col, 'Email', validate_email))
    
    # URL validation
    url_columns = [col for col in df.columns if 'url' in col.lower() or 'link' in col.lower() or 'website' in col.lower()]
    for col in url_columns:
        validation_rules.append((col, 'URL', validate_url))
    
    if not validation_rules:
        viz.text("No columns found for rule validation!", 
                win="rule_check_summary", title="Rule Validation Summary")
        return
    
    # Perform validations
    validation_results = {}
    
    for col, rule_type, validator in validation_rules:
        if col in df.columns:
            results = validator(df[col])
            if 'error' not in results:
                validation_results[f"{col} ({rule_type})"] = results
    
    if not validation_results:
        viz.text("No valid data found for rule validation!", 
                win="rule_check_summary", title="Rule Validation Summary")
        return
    
    # Create validation summary
    summary_data = []
    for rule_name, results in validation_results.items():
        summary_data.append({
            'Rule': rule_name,
            'Total_Records': results['total_count'],
            'Non_Empty': results['non_empty_count'],
            'Valid_Format': results['valid_format_count'],
            'Invalid_Format': results['invalid_format_count'],
            'Compliance_Rate': results['valid_format_rate'],
            'Empty_Rate': (results['empty_count'] / results['total_count'] * 100) if results['total_count'] > 0 else 0
        })
    
    summary_df = pd.DataFrame(summary_data)
    
    # 1. Compliance rate overview
    fig_compliance = px.bar(
        summary_df,
        x='Rule',
        y='Compliance_Rate',
        title='Rule Compliance Rates',
        labels={'Compliance_Rate': 'Compliance Rate (%)', 'Rule': 'Validation Rules'},
        color='Compliance_Rate',
        color_continuous_scale='RdYlGn',
        range_color=[0, 100]
    )
    fig_compliance.update_xaxes(tickangle=45)
    fig_compliance.add_hline(y=95, line_dash="dash", line_color="green", 
                            annotation_text="95% Target")
    viz.plotlyplot(fig_compliance, win="compliance_rates")
    viz.export(fig_compliance, "rule_compliance_rates")
    
    # 2. Data completeness vs compliance
    fig_scatter = px.scatter(
        summary_df,
        x='Empty_Rate',
        y='Compliance_Rate',
        size='Total_Records',
        hover_data=['Rule'],
        title='Data Completeness vs Rule Compliance',
        labels={
            'Empty_Rate': 'Empty Rate (%)',
            'Compliance_Rate': 'Compliance Rate (%)',
            'Total_Records': 'Total Records'
        },
        color='Compliance_Rate',
        color_continuous_scale='RdYlGn'
    )
    viz.plotlyplot(fig_scatter, win="completeness_vs_compliance")
    viz.export(fig_scatter, "completeness_vs_compliance")
    
    # 3. Detailed breakdown for each rule
    breakdown_data = []
    for rule_name, results in validation_results.items():
        breakdown_data.extend([
            {'Rule': rule_name, 'Category': 'Valid Format', 'Count': results['valid_format_count']},
            {'Rule': rule_name, 'Category': 'Invalid Format', 'Count': results['invalid_format_count']},
            {'Rule': rule_name, 'Category': 'Empty/Missing', 'Count': results['empty_count']}
        ])
    
    breakdown_df = pd.DataFrame(breakdown_data)
    
    fig_breakdown = px.bar(
        breakdown_df,
        x='Rule',
        y='Count',
        color='Category',
        title='Detailed Rule Validation Breakdown',
        labels={'Count': 'Number of Records', 'Rule': 'Validation Rules'},
        color_discrete_map={
            'Valid Format': 'green',
            'Invalid Format': 'red',
            'Empty/Missing': 'gray'
        }
    )
    fig_breakdown.update_xaxes(tickangle=45)
    viz.plotlyplot(fig_breakdown, win="validation_breakdown")
    viz.export(fig_breakdown, "validation_breakdown")
    
    # 4. Compliance trend (if multiple similar rules)
    rule_types = {}
    for rule_name in validation_results.keys():
        rule_type = rule_name.split('(')[1].split(')')[0]
        if rule_type not in rule_types:
            rule_types[rule_type] = []
        rule_types[rule_type].append(rule_name)
    
    if any(len(rules) > 1 for rules in rule_types.values()):
        type_summary = []
        for rule_type, rules in rule_types.items():
            if len(rules) > 1:
                avg_compliance = np.mean([validation_results[rule]['valid_format_rate'] for rule in rules])
                total_records = sum([validation_results[rule]['total_count'] for rule in rules])
                type_summary.append({
                    'Rule_Type': rule_type,
                    'Average_Compliance': avg_compliance,
                    'Total_Records': total_records,
                    'Number_of_Fields': len(rules)
                })
        
        if type_summary:
            type_df = pd.DataFrame(type_summary)
            
            fig_types = px.bar(
                type_df,
                x='Rule_Type',
                y='Average_Compliance',
                title='Average Compliance by Rule Type',
                labels={'Average_Compliance': 'Average Compliance (%)', 'Rule_Type': 'Rule Types'},
                color='Average_Compliance',
                color_continuous_scale='RdYlGn'
            )
            viz.plotlyplot(fig_types, win="compliance_by_type")
            viz.export(fig_types, "compliance_by_type")
    
    # Create summary text
    total_rules = len(validation_results)
    avg_compliance = summary_df['Compliance_Rate'].mean()
    high_compliance_rules = (summary_df['Compliance_Rate'] >= 95).sum()
    low_compliance_rules = (summary_df['Compliance_Rate'] < 50).sum()
    
    summary_text = f"""
    <h3>Rule Validation Summary</h3>
    <p><strong>Total validation rules:</strong> {total_rules}</p>
    <p><strong>Average compliance rate:</strong> {avg_compliance:.1f}%</p>
    <p><strong>High compliance rules (≥95%):</strong> {high_compliance_rules}</p>
    <p><strong>Low compliance rules (<50%):</strong> {low_compliance_rules}</p>
    
    <h4>Rule Details:</h4>
    """
    
    for _, row in summary_df.iterrows():
        summary_text += f"""
        <p><strong>{row['Rule']}:</strong></p>
        <ul>
            <li>Total records: {row['Total_Records']:,}</li>
            <li>Non-empty: {row['Non_Empty']:,}</li>
            <li>Valid format: {row['Valid_Format']:,} ({row['Compliance_Rate']:.1f}%)</li>
            <li>Invalid format: {row['Invalid_Format']:,}</li>
            <li>Empty rate: {row['Empty_Rate']:.1f}%</li>
        </ul>
        """
    
    # Add examples of invalid data
    summary_text += f"""
    <h4>Examples of Invalid Data:</h4>
    """
    
    for rule_name, results in validation_results.items():
        if results['invalid_examples']:
            summary_text += f"""
            <p><strong>{rule_name}:</strong></p>
            <ul>
            """
            for example in results['invalid_examples'][:5]:
                summary_text += f"<li>{example}</li>"
            summary_text += "</ul>"
    
    viz.text(summary_text, win="rule_check_summary", title="Rule Validation Summary")
