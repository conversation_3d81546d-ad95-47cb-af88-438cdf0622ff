"""Outlier detection using IsolationForest."""

import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from typing import TYPE_CHECKING, List, Dict
from ..analysis_core import iso_outliers

if TYPE_CHECKING:
    from ..visualizer import Visualizer


def detect_outliers_multivariate(df: pd.DataFrame, columns: List[str]) -> pd.Series:
    """Detect outliers using multivariate IsolationForest.
    
    Args:
        df: Input DataFrame
        columns: List of numeric columns to analyze
        
    Returns:
        Boolean series indicating outliers
    """
    from sklearn.ensemble import IsolationForest
    
    # Select numeric data and drop missing values
    data = df[columns].select_dtypes(include=[np.number]).dropna()
    
    if len(data) == 0:
        return pd.Series([False] * len(df), index=df.index)
    
    # Fit IsolationForest
    model = IsolationForest(contamination=0.05, random_state=42)
    predictions = model.fit_predict(data)
    
    # Create result series
    result = pd.Series([False] * len(df), index=df.index)
    result.loc[data.index] = (predictions == -1)
    
    return result


def run(df: pd.DataFrame, viz: "Visualizer") -> None:
    """Detect outliers and create visualizations.
    
    Args:
        df: Input DataFrame
        viz: Visualizer instance
    """
    # Find numeric columns
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    
    if len(numeric_cols) == 0:
        viz.text("No numeric columns found for outlier detection!", 
                win="outlier_summary", title="Outlier Detection Summary")
        return
    
    # Remove derived columns that might skew results
    exclude_cols = ['Row_Completeness', 'Description_Length', 'Title_Length']
    numeric_cols = [col for col in numeric_cols if col not in exclude_cols]
    
    if len(numeric_cols) == 0:
        viz.text("No suitable numeric columns found for outlier detection!", 
                win="outlier_summary", title="Outlier Detection Summary")
        return
    
    # Detect outliers for each column individually
    outlier_results = {}
    
    for col in numeric_cols:
        if df[col].notna().sum() > 10:  # Need at least 10 non-null values
            outliers = iso_outliers(df[col])
            if outliers.any():
                outlier_results[col] = {
                    'outliers': outliers,
                    'count': outliers.sum(),
                    'percentage': (outliers.sum() / len(df) * 100)
                }
    
    if not outlier_results:
        viz.text("No outliers detected in any numeric columns!", 
                win="outlier_summary", title="Outlier Detection Summary")
        return
    
    # Create summary of outliers by column
    outlier_summary = pd.DataFrame([
        {
            'Column': col,
            'Outlier_Count': data['count'],
            'Outlier_Percentage': data['percentage']
        }
        for col, data in outlier_results.items()
    ]).sort_values('Outlier_Count', ascending=False)
    
    # Create bar chart of outliers by column
    fig_outliers = px.bar(
        outlier_summary,
        x='Column',
        y='Outlier_Count',
        title='Outliers Detected by Column',
        labels={'Outlier_Count': 'Number of Outliers', 'Column': 'Columns'},
        color='Outlier_Count',
        color_continuous_scale='Reds'
    )
    fig_outliers.update_xaxes(tickangle=45)
    viz.plotlyplot(fig_outliers, win="outlier_counts")
    viz.export(fig_outliers, "outlier_detection")
    
    # Create detailed analysis for top outlier columns
    top_outlier_cols = outlier_summary.head(3)['Column'].tolist()
    
    for i, col in enumerate(top_outlier_cols):
        if col in df.columns:
            outlier_mask = outlier_results[col]['outliers']
            
            # Create box plot showing outliers
            fig_box = px.box(
                y=df[col],
                title=f'Box Plot: {col} (Outliers Highlighted)',
                labels={'y': col}
            )
            
            # Add outlier points
            outlier_values = df.loc[outlier_mask, col].dropna()
            if len(outlier_values) > 0:
                fig_box.add_trace(
                    go.Scatter(
                        y=outlier_values,
                        x=['outliers'] * len(outlier_values),
                        mode='markers',
                        marker=dict(color='red', size=8),
                        name='Detected Outliers'
                    )
                )
            
            viz.plotlyplot(fig_box, win=f"outlier_box_{col}")
            viz.export(fig_box, f"outlier_boxplot_{col}")
    
    # Multivariate outlier detection
    if len(numeric_cols) >= 2:
        multivariate_outliers = detect_outliers_multivariate(df, numeric_cols[:5])  # Limit to 5 columns
        mv_outlier_count = multivariate_outliers.sum()
        
        if mv_outlier_count > 0:
            # Create scatter plot for first two numeric columns
            if len(numeric_cols) >= 2:
                col1, col2 = numeric_cols[0], numeric_cols[1]
                
                fig_scatter = px.scatter(
                    df,
                    x=col1,
                    y=col2,
                    color=multivariate_outliers,
                    title=f'Multivariate Outliers: {col1} vs {col2}',
                    color_discrete_map={True: 'red', False: 'blue'},
                    labels={True: 'Outlier', False: 'Normal'}
                )
                viz.plotlyplot(fig_scatter, win="multivariate_outliers")
                viz.export(fig_scatter, "multivariate_outliers")
    
    # Analyze outlier overlap
    if len(outlier_results) > 1:
        # Find rows that are outliers in multiple columns
        total_outlier_flags = pd.DataFrame({
            col: data['outliers'] for col, data in outlier_results.items()
        })
        
        outlier_counts_per_row = total_outlier_flags.sum(axis=1)
        
        # Create histogram of outlier counts per row
        fig_overlap = px.histogram(
            x=outlier_counts_per_row,
            nbins=min(10, outlier_counts_per_row.max() + 1),
            title='Distribution of Outlier Flags per Row',
            labels={'x': 'Number of Outlier Flags per Row', 'y': 'Number of Rows'},
            color_discrete_sequence=['orange']
        )
        viz.plotlyplot(fig_overlap, win="outlier_overlap")
        viz.export(fig_overlap, "outlier_overlap")
    
    # Create summary text
    total_outliers = sum(data['count'] for data in outlier_results.values())
    affected_rows = len(set().union(*[data['outliers'][data['outliers']].index 
                                    for data in outlier_results.values()]))
    
    summary_text = f"""
    <h3>Outlier Detection Summary</h3>
    <p><strong>Columns analyzed:</strong> {len(numeric_cols)}</p>
    <p><strong>Columns with outliers:</strong> {len(outlier_results)}</p>
    <p><strong>Total outlier instances:</strong> {total_outliers}</p>
    <p><strong>Unique rows with outliers:</strong> {affected_rows} ({affected_rows/len(df)*100:.1f}%)</p>
    
    <h4>Outliers by Column:</h4>
    """
    
    for _, row in outlier_summary.iterrows():
        summary_text += f"<p>• {row['Column']}: {row['Outlier_Count']} outliers ({row['Outlier_Percentage']:.1f}%)</p>"
    
    if len(numeric_cols) >= 2 and 'mv_outlier_count' in locals():
        summary_text += f"""
        <h4>Multivariate Analysis:</h4>
        <p>Multivariate outliers detected: {mv_outlier_count} ({mv_outlier_count/len(df)*100:.1f}%)</p>
        """
    
    # Show most extreme outliers
    if outlier_results:
        most_outliers_col = outlier_summary.iloc[0]['Column']
        outlier_mask = outlier_results[most_outliers_col]['outliers']
        extreme_values = df.loc[outlier_mask, most_outliers_col].dropna()
        
        if len(extreme_values) > 0:
            summary_text += f"""
            <h4>Most Extreme Outliers in {most_outliers_col}:</h4>
            <p>Range: {extreme_values.min():.2f} to {extreme_values.max():.2f}</p>
            <p>Mean of outliers: {extreme_values.mean():.2f}</p>
            """
    
    viz.text(summary_text, win="outlier_summary", title="Outlier Detection Summary")
