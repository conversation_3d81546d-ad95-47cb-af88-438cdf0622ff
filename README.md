# Data Quality Analysis System

A comprehensive, multi-dimensional data quality assessment system with real-time visualization capabilities using Visdom. This system provides automated analysis of data completeness, consistency, accuracy, and integrity across various data types.

## Features

### 🔍 **Comprehensive Analysis Modules**
- **Missing Value Analysis**: Detailed missing data patterns and visualization
- **Data Completeness**: Field and row-level completion rate analysis
- **Failure Pattern Detection**: Automated data quality issue identification
- **Duplicate Detection**: Multi-strategy duplicate record identification
- **Outlier Detection**: Statistical outlier identification using IsolationForest
- **Field Correlation Analysis**: Mixed data type correlation using phik
- **Time Trend Analysis**: Temporal pattern analysis and data quality over time
- **Categorical Distribution**: Category distribution and diversity analysis
- **Text Length Analysis**: Text field quality and length distribution
- **Rule Validation**: Format compliance checking (ISBN, IMDb ID, Email, URL)

### 📊 **Interactive Visualization**
- Real-time web dashboard using Visdom
- Interactive charts with Plotly
- Automatic report export (PNG/HTML)
- Comprehensive summary statistics

### 🏗️ **Modular Architecture**
- Pluggable analysis modules
- Universal data loader (CSV, Excel, JSON)
- Configurable preprocessing pipeline
- Docker support for easy deployment

## Quick Start

### Prerequisites
- Python 3.11+
- Docker (optional)

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd data-quality-analysis
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Prepare your data**
   - Place your data file in the `data/` directory
   - Update `app/config.py` with your data file path
   - Supported formats: CSV, Excel (.xls, .xlsx), JSON

4. **Start Visdom server**
```bash
python -m visdom.server --port 8097
```

5. **Run the analysis**
```bash
python app/main.py
```

6. **View results**
   - Open http://localhost:8097 in your browser
   - Check the `reports/` directory for exported visualizations

### Docker Deployment

```bash
# Run complete analysis with Visdom
docker-compose up data-quality-analysis

# Or run just Visdom server for development
docker-compose --profile dev up visdom-only
```

## Configuration

Edit `app/config.py` to customize:

```python
DATA_PATH = Path("./data/your_data_file.csv")  # Your data file
VISDOM_SERVER = "http://localhost"             # Visdom server URL
VISDOM_PORT = 8097                             # Visdom port
ENV_NAME = "quality_dashboard"                 # Visdom environment name
EXPORT_REPORTS = True                          # Auto-export reports
```

## Project Structure

```
project/
├── data/                         # Data files
│   └── movie_book_info.csv      # Sample data
├── app/
│   ├── config.py                # Configuration
│   ├── loader.py                # Data loading
│   ├── preprocess.py            # Data preprocessing
│   ├── analysis_core.py         # Core analysis utilities
│   ├── visualizer.py            # Visdom visualization wrapper
│   ├── main.py                  # Main entry point
│   └── analyses/                # Analysis modules
│       ├── missingness.py       # Missing value analysis
│       ├── completeness.py      # Completeness analysis
│       ├── failure_patterns.py  # Failure pattern detection
│       ├── dup_detect.py        # Duplicate detection
│       ├── outlier_detect.py    # Outlier detection
│       ├── field_corr.py        # Field correlation
│       ├── time_trend.py        # Time trend analysis
│       ├── cat_distribution.py  # Categorical analysis
│       ├── text_len.py          # Text length analysis
│       └── rule_check.py        # Rule validation
├── reports/                     # Exported visualizations
├── tests/                       # Unit tests
├── docker-compose.yml           # Docker configuration
├── requirements.txt             # Python dependencies
└── README.md                    # This file
```

## Analysis Modules

### 1. Missing Value Analysis (`missingness.py`)
- Missing value matrix visualization
- Missing value correlation heatmap
- Column-wise missing statistics
- Missing value patterns

### 2. Data Completeness (`completeness.py`)
- Field completion rates
- Row completion distribution
- Completion rate categories
- Quality thresholds analysis

### 3. Failure Pattern Detection (`failure_patterns.py`)
- Empty string detection
- Placeholder value identification
- Invalid value patterns
- Data corruption indicators

### 4. Duplicate Detection (`dup_detect.py`)
- Multiple detection strategies
- Title+Author, ISBN, IMDb ID based detection
- Duplicate group analysis
- Overlap pattern visualization

### 5. Outlier Detection (`outlier_detect.py`)
- Univariate outlier detection
- Multivariate outlier analysis
- Statistical distribution analysis
- Outlier impact assessment

### 6. Field Correlation (`field_corr.py`)
- Mixed data type correlation using phik
- Correlation strength analysis
- Network visualization
- Relationship discovery

### 7. Time Trend Analysis (`time_trend.py`)
- Temporal pattern identification
- Seasonal analysis
- Data quality trends over time
- Growth rate analysis

### 8. Categorical Distribution (`cat_distribution.py`)
- Category frequency analysis
- Diversity metrics (Shannon entropy)
- Cardinality analysis
- Distribution visualization

### 9. Text Length Analysis (`text_len.py`)
- Text length distribution
- Word count analysis
- Quality issue identification
- Length category analysis

### 10. Rule Validation (`rule_check.py`)
- ISBN format validation
- IMDb ID format checking
- Email format validation
- URL format compliance

## Testing

Run the test suite:

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test file
pytest tests/test_loader.py
```

## Sample Data

The system includes sample movie/book data with the following fields:
- Title, Author, Director, Genre
- Year, Rating, Pages, Runtime
- Description, ISBN, IMDb_ID
- Publication_Date, Price

## Extending the System

### Adding New Analysis Modules

1. Create a new file in `app/analyses/`
2. Implement a `run(df, viz)` function
3. Add the module to `MODULES` list in `main.py`

Example:
```python
def run(df: pd.DataFrame, viz: "Visualizer") -> None:
    """Your analysis logic here."""
    # Perform analysis
    results = analyze_data(df)
    
    # Create visualization
    fig = create_plot(results)
    viz.plotlyplot(fig, win="your_analysis")
    viz.export(fig, "your_analysis")
    
    # Create summary
    summary = create_summary(results)
    viz.text(summary, win="your_summary", title="Your Analysis")
```

### Custom Data Loaders

Extend `loader.py` to support additional file formats:

```python
def load_data(path: Union[str, pathlib.Path]) -> pd.DataFrame:
    path = pathlib.Path(path)
    
    if path.suffix == ".parquet":
        return pd.read_parquet(path)
    # ... existing code
```

## Dependencies

- **pandas**: Data manipulation and analysis
- **numpy**: Numerical computing
- **visdom**: Web-based visualization
- **plotly**: Interactive plotting
- **missingno**: Missing data visualization
- **scikit-learn**: Machine learning utilities
- **phik**: Correlation analysis for mixed data types
- **python-slugify**: String processing utilities
- **pytest**: Testing framework

## License

This project is open source and available under the MIT License.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## Support

For issues and questions:
1. Check the existing issues
2. Create a new issue with detailed description
3. Include sample data and error messages

---

**Built with ❤️ for data quality professionals**
