下面给出一套 **可直接粘贴进 Claude Code 的完整技术框架 outline**，涵盖目录结构、关键模块、核心函数签名、依赖、CI 与部署脚本，帮助你把已采集的元数据快速导入 Pandas 并通过 **Meta Visdom** 进行 11 类数据质量分析。[9][10]

***

## 目录结构
```
project/
├── data/                         # 原始 CSV/Excel/JSON
│   └── movie_book_info.csv
├── app/
│   ├── __init__.py
│   ├── config.py                 # 常量 & 路径
│   ├── loader.py                 # 统一数据加载
│   ├── preprocess.py             # 清洗/特征工程
│   ├── analysis_core.py          # 公共统计工具
│   ├── analyses/                 # 各分析模块
│   │   ├── missingness.py
│   │   ├── completeness.py
│   │   ├── failure_patterns.py
│   │   ├── dup_detect.py
│   │   ├── outlier_detect.py
│   │   ├── field_corr.py
│   │   ├── time_trend.py
│   │   ├── cat_distribution.py
│   │   ├── text_len.py           # 简介长度分布
│   │   └── rule_check.py         # ISBN / IMDb ID 合法率
│   ├── visualizer.py             # Visdom 封装
│   └── main.py                   # 入口
├── reports/                      # 自动导出图表
├── tests/                        # Pytest 单元测试
├── docker-compose.yml
├── requirements.txt
└── README.md
```

***

## requirements.txt
```
pandas             # CSV/Excel/JSON 导入[46][48]
numpy
visdom             # Web 可视化服务器[24][41]
plotly             # 交互式图表
missingno          # 缺失值矩阵/热力图
scikit-learn       # IsolationForest 异常检测
phik               # 相关性矩阵（支持混合类型）
python-slugify     # 规则校验辅助
pytest
```

***

## 核心配置（config.py）
```python
from pathlib import Path
DATA_PATH      = Path("./data/movie_book_info.csv")
VISDOM_SERVER  = "http://localhost"
VISDOM_PORT    = 8097
ENV_NAME       = "quality_dashboard"
RANDOM_SEED    = 42
EXPORT_REPORTS = True          # 是否自动保存 PNG/HTML
```

***

## 通用加载器（loader.py）
```python
import pandas as pd, pathlib

def load_data(path: str | pathlib.Path) -> pd.DataFrame:
    """Auto-detect file type and return DataFrame."""
    path = pathlib.Path(path)
    if path.suffix == ".csv":
        return pd.read_csv(path)                      # csv 导入[46]
    if path.suffix in {".xls", ".xlsx"}:
        return pd.read_excel(path, sheet_name=0)
    if path.suffix == ".json":
        return pd.read_json(path, orient="records")
    raise ValueError(f"Unsupported file type: {path.suffix}")
```

***

## 公共工具（analysis_core.py）
```python
import pandas as pd, numpy as np

def field_completion(df: pd.DataFrame) -> pd.Series:
    """字段完成率 %"""
    return (df.notna().mean()*100).round(2)

def row_completion(df: pd.DataFrame) -> pd.Series:
    """行级完整率 %"""
    return (df.notna().mean(axis=1)*100).round(2)

def iso_outliers(series: pd.Series, contamination=0.02) -> pd.Series:
    """IsolationForest 返回 True=异常值"""
    from sklearn.ensemble import IsolationForest
    model = IsolationForest(contamination=contamination, random_state=0)
    return pd.Series(model.fit_predict(series.values.reshape(-1,1))==-1, index=series.index)
```

***

## 11 个分析模块接口（示例：dup_detect.py）
```python
def run(df, viz):
    """检测基于 Title+Author 的重复记录并可视化"""
    dup_flag = df.duplicated(subset=["Title", "Author"], keep=False)
    dup_df   = df[dup_flag]
    counts   = dup_df["Title"].value_counts().nlargest(10)

    import plotly.express as px
    bar = px.bar(x=counts.index, y=counts.values,
                 labels={"x":"重复标题","y":"出现次数"}, title="重复记录 Top-10")
    viz.plotlyplot(bar)
```
其余模块同样实现 `run(df, viz)`，输出 **Matplotlib / Plotly** 图表到 Visdom 面板。

***

## 可视化封装（visualizer.py）
```python
import visdom, plotly.io as pio, matplotlib.pyplot as plt

viz = visdom.Visdom(server=config.VISDOM_SERVER, port=config.VISDOM_PORT,
                    env=config.ENV_NAME)

def matplot(fig: plt.Figure, win: str=None, title: str=""):
    viz.matplot(fig, win=win, opts={"title": title})

def plotlyplot(fig, win: str=None):
    viz.plotlyplot(fig, win=win)

def export(fig, name: str):
    """保存 PNG/HTML 到 reports/"""
    if config.EXPORT_REPORTS:
        pio.write_image(fig, f"reports/{name}.png")
        fig.write_html(f"reports/{name}.html")
```

***

## 主程序（main.py）
```python
from app import config, loader, preprocess, visualizer
from app.analyses import (missingness, completeness,
                          failure_patterns, dup_detect,
                          outlier_detect, field_corr,
                          time_trend, cat_distribution,
                          text_len, rule_check)

MODULES = [missingness, completeness, failure_patterns,
           dup_detect, outlier_detect, field_corr,
           time_trend, cat_distribution, text_len, rule_check]

def run():
    df = loader.load_data(config.DATA_PATH)
    df = preprocess.clean(df)          # 类型转换 & 额外特征
    for mod in MODULES:
        mod.run(df, visualizer.viz)

if __name__ == "__main__":
    run()
```

***

## Docker-compose
```yaml
version: "3"
services:
  visdom:
    image: python:3.11-slim
    ports: ["8097:8097"]
    volumes:
      - ./app:/app
      - ./data:/data
      - ./reports:/reports
    command: >
      bash -c "
      pip install -r /app/../requirements.txt &&
      python -m visdom.server --env=$ENV_NAME & 
      python /app/main.py"
```

***

## CI 流程（简述）
1. **Install**：`pip install -r requirements.txt`
2. **Lint & Test**：`pytest -q`
3. **Run main.py** → 产出 `reports/*.png|html`
4. **Artifacts**：上传报告供审阅

***

### Claude Code 指令模板
```
请在空白工作区生成上方 outline 涉及的所有文件并填充完整代码：  
1. 目录与文件应保持一致；  
2. 每个分析模块包含 docstring、类型注解、可独立运行的 run(df,viz)；  
3. 确保 pytest 通过；  
4. 生成 20 行示例 CSV 至 data/；  
5. 完成后输出 DONE。
```

按此 **技术框架细节 + 完整 outline** 直接执行即可得到一个具备 **多维数据质量评估**、可 **Visdom 实时浏览**、且 **完全开源免费** 的分析系统。