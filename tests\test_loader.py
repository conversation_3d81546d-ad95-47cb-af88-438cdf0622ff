"""Tests for the data loader module."""

import pytest
import pandas as pd
import tempfile
import json
from pathlib import Path
import sys

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.loader import load_data


class TestLoader:
    """Test cases for the data loader."""
    
    def test_load_csv(self):
        """Test loading CSV files."""
        # Create temporary CSV file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write("name,age,city\n")
            f.write("Alice,25,New York\n")
            f.write("Bob,30,London\n")
            csv_path = f.name
        
        try:
            df = load_data(csv_path)
            assert isinstance(df, pd.DataFrame)
            assert len(df) == 2
            assert list(df.columns) == ['name', 'age', 'city']
            assert df.iloc[0]['name'] == 'Alice'
        finally:
            Path(csv_path).unlink()
    
    def test_load_json(self):
        """Test loading JSON files."""
        # Create temporary JSON file
        data = [
            {"name": "Alice", "age": 25, "city": "New York"},
            {"name": "Bob", "age": 30, "city": "London"}
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(data, f)
            json_path = f.name
        
        try:
            df = load_data(json_path)
            assert isinstance(df, pd.DataFrame)
            assert len(df) == 2
            assert list(df.columns) == ['name', 'age', 'city']
            assert df.iloc[0]['name'] == 'Alice'
        finally:
            Path(json_path).unlink()
    
    def test_unsupported_format(self):
        """Test error handling for unsupported file formats."""
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            txt_path = f.name
        
        try:
            with pytest.raises(ValueError, match="Unsupported file type"):
                load_data(txt_path)
        finally:
            Path(txt_path).unlink()
    
    def test_pathlib_path(self):
        """Test that pathlib.Path objects work."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write("col1,col2\n1,2\n")
            csv_path = Path(f.name)
        
        try:
            df = load_data(csv_path)
            assert isinstance(df, pd.DataFrame)
            assert len(df) == 1
        finally:
            csv_path.unlink()
