"""Data completeness analysis with field and row completion rates."""

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from typing import TYPE_CHECKING
from ..analysis_core import field_completion, row_completion

if TYPE_CHECKING:
    from ..visualizer import Visualizer


def run(df: pd.DataFrame, viz: "Visualizer") -> None:
    """Analyze data completeness and create visualizations.
    
    Args:
        df: Input DataFrame
        viz: Visualizer instance
    """
    # Calculate field completion rates
    field_comp = field_completion(df)
    field_comp_df = pd.DataFrame({
        'Field': field_comp.index,
        'Completion_Rate': field_comp.values
    }).sort_values('Completion_Rate', ascending=True)
    
    # Create field completion bar chart
    fig_field = px.bar(
        field_comp_df,
        x='Completion_Rate',
        y='Field',
        orientation='h',
        title='Field Completion Rates',
        labels={'Completion_Rate': 'Completion Rate (%)', 'Field': 'Fields'},
        color='Completion_Rate',
        color_continuous_scale='RdYlGn'
    )
    fig_field.update_layout(height=max(400, len(field_comp_df) * 25))
    viz.plotlyplot(fig_field, win="field_completion")
    viz.export(fig_field, "field_completion_rates")
    
    # Calculate row completion rates
    row_comp = row_completion(df)
    
    # Create row completion histogram
    fig_row_hist = px.histogram(
        x=row_comp,
        nbins=20,
        title='Distribution of Row Completion Rates',
        labels={'x': 'Row Completion Rate (%)', 'y': 'Number of Rows'},
        color_discrete_sequence=['skyblue']
    )
    viz.plotlyplot(fig_row_hist, win="row_completion_hist")
    viz.export(fig_row_hist, "row_completion_distribution")
    
    # Create completion rate categories
    completion_categories = pd.cut(
        row_comp,
        bins=[0, 25, 50, 75, 90, 100],
        labels=['Very Low (0-25%)', 'Low (25-50%)', 'Medium (50-75%)', 'High (75-90%)', 'Very High (90-100%)'],
        include_lowest=True
    )
    
    category_counts = completion_categories.value_counts()
    
    # Create pie chart of completion categories
    fig_pie = px.pie(
        values=category_counts.values,
        names=category_counts.index,
        title='Row Completion Rate Categories',
        color_discrete_sequence=px.colors.qualitative.Set3
    )
    viz.plotlyplot(fig_pie, win="completion_categories")
    viz.export(fig_pie, "completion_categories")
    
    # Calculate statistics
    avg_field_completion = field_comp.mean()
    min_field_completion = field_comp.min()
    max_field_completion = field_comp.max()
    
    avg_row_completion = row_comp.mean()
    min_row_completion = row_comp.min()
    max_row_completion = row_comp.max()
    
    # Find most and least complete fields
    most_complete_field = field_comp.idxmax()
    least_complete_field = field_comp.idxmin()
    
    # Create summary text
    summary_text = f"""
    <h3>Data Completeness Summary</h3>
    
    <h4>Field-Level Completeness</h4>
    <p><strong>Average field completion:</strong> {avg_field_completion:.2f}%</p>
    <p><strong>Most complete field:</strong> {most_complete_field} ({field_comp[most_complete_field]:.2f}%)</p>
    <p><strong>Least complete field:</strong> {least_complete_field} ({field_comp[least_complete_field]:.2f}%)</p>
    <p><strong>Range:</strong> {min_field_completion:.2f}% - {max_field_completion:.2f}%</p>
    
    <h4>Row-Level Completeness</h4>
    <p><strong>Average row completion:</strong> {avg_row_completion:.2f}%</p>
    <p><strong>Range:</strong> {min_row_completion:.2f}% - {max_row_completion:.2f}%</p>
    <p><strong>Rows with >90% completion:</strong> {(row_comp > 90).sum():,} ({(row_comp > 90).mean()*100:.1f}%)</p>
    <p><strong>Rows with <50% completion:</strong> {(row_comp < 50).sum():,} ({(row_comp < 50).mean()*100:.1f}%)</p>
    """
    
    viz.text(summary_text, win="completeness_summary", title="Completeness Summary")
