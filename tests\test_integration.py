"""Integration tests for the complete system."""

import pytest
import pandas as pd
import sys
from pathlib import Path
from unittest.mock import Mock, patch

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from app import loader, preprocess
from app.analyses import missingness, completeness, dup_detect


class TestIntegration:
    """Integration test cases."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample data for testing."""
        return pd.DataFrame({
            'Title': ['Book 1', 'Book 2', 'Book 1', 'Book 3', None],
            'Author': ['Author A', 'Author B', 'Author A', 'Author C', 'Author D'],
            'Year': [2020, 2021, 2020, 2022, None],
            'Rating': [4.5, 3.2, 4.5, 4.8, 2.1],
            'Description': ['Long description here', 'Short', None, 'Medium length desc', 'Brief'],
            'ISBN': ['978-0123456789', 'invalid-isbn', '978-0123456789', '978-9876543210', None]
        })
    
    @pytest.fixture
    def mock_visualizer(self):
        """Create a mock visualizer for testing."""
        mock_viz = Mock()
        mock_viz.plotlyplot = Mock()
        mock_viz.matplot = Mock()
        mock_viz.text = Mock()
        mock_viz.export = Mock()
        return mock_viz
    
    def test_data_loading_and_preprocessing(self, sample_data):
        """Test the complete data loading and preprocessing pipeline."""
        # Test preprocessing
        df_clean = preprocess.clean(sample_data)
        
        # Verify preprocessing results
        assert len(df_clean) == len(sample_data)
        assert 'Row_Completeness' in df_clean.columns
        assert 'Description_Length' in df_clean.columns
        
        # Check that text fields are cleaned
        assert not df_clean['Title'].iloc[0].startswith(' ')
    
    def test_missingness_analysis(self, sample_data, mock_visualizer):
        """Test missingness analysis module."""
        df_clean = preprocess.clean(sample_data)
        
        # Should run without errors
        missingness.run(df_clean, mock_visualizer)
        
        # Verify visualizer was called
        assert mock_visualizer.plotlyplot.called
        assert mock_visualizer.text.called
    
    def test_completeness_analysis(self, sample_data, mock_visualizer):
        """Test completeness analysis module."""
        df_clean = preprocess.clean(sample_data)
        
        # Should run without errors
        completeness.run(df_clean, mock_visualizer)
        
        # Verify visualizer was called
        assert mock_visualizer.plotlyplot.called
        assert mock_visualizer.text.called
    
    def test_duplicate_detection(self, sample_data, mock_visualizer):
        """Test duplicate detection module."""
        df_clean = preprocess.clean(sample_data)
        
        # Should run without errors
        dup_detect.run(df_clean, mock_visualizer)
        
        # Verify visualizer was called (duplicates should be found)
        assert mock_visualizer.plotlyplot.called
        assert mock_visualizer.text.called
    
    def test_empty_dataframe_handling(self, mock_visualizer):
        """Test that modules handle empty DataFrames gracefully."""
        empty_df = pd.DataFrame()
        
        # All modules should handle empty data gracefully
        missingness.run(empty_df, mock_visualizer)
        completeness.run(empty_df, mock_visualizer)
        dup_detect.run(empty_df, mock_visualizer)
        
        # Should have called text method to show "no data" messages
        assert mock_visualizer.text.called
    
    def test_data_with_all_nulls(self, mock_visualizer):
        """Test handling of DataFrame with all null values."""
        null_df = pd.DataFrame({
            'A': [None, None, None],
            'B': [None, None, None],
            'C': [None, None, None]
        })
        
        # Should handle gracefully
        missingness.run(null_df, mock_visualizer)
        completeness.run(null_df, mock_visualizer)
        
        assert mock_visualizer.text.called or mock_visualizer.plotlyplot.called
    
    def test_single_row_dataframe(self, mock_visualizer):
        """Test handling of single-row DataFrame."""
        single_row_df = pd.DataFrame({
            'Title': ['Single Book'],
            'Author': ['Single Author'],
            'Year': [2023]
        })
        
        # Should handle gracefully
        missingness.run(single_row_df, mock_visualizer)
        completeness.run(single_row_df, mock_visualizer)
        dup_detect.run(single_row_df, mock_visualizer)
        
        # Should complete without errors
        assert True  # If we get here, no exceptions were raised
