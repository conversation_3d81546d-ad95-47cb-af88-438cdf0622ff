"""Categorical data distribution analysis."""

import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from typing import TYPE_CHECKING, List, Dict

if TYPE_CHECKING:
    from ..visualizer import Visualizer


def analyze_categorical_column(series: pd.Series) -> Dict:
    """Analyze a single categorical column.
    
    Args:
        series: Pandas series to analyze
        
    Returns:
        Dictionary with analysis results
    """
    # Remove null values for analysis
    clean_series = series.dropna()
    
    if len(clean_series) == 0:
        return {'error': 'No valid data'}
    
    # Calculate basic statistics
    value_counts = clean_series.value_counts()
    
    results = {
        'total_count': len(series),
        'valid_count': len(clean_series),
        'null_count': len(series) - len(clean_series),
        'unique_count': len(value_counts),
        'value_counts': value_counts,
        'most_common': value_counts.index[0] if len(value_counts) > 0 else None,
        'most_common_count': value_counts.iloc[0] if len(value_counts) > 0 else 0,
        'least_common': value_counts.index[-1] if len(value_counts) > 0 else None,
        'least_common_count': value_counts.iloc[-1] if len(value_counts) > 0 else 0
    }
    
    # Calculate diversity metrics
    if len(value_counts) > 0:
        # Shannon entropy (diversity index)
        proportions = value_counts / len(clean_series)
        entropy = -np.sum(proportions * np.log2(proportions + 1e-10))
        results['entropy'] = entropy
        
        # Concentration ratio (top 3 categories)
        top3_ratio = value_counts.head(3).sum() / len(clean_series)
        results['top3_concentration'] = top3_ratio
        
        # Gini coefficient (inequality measure)
        sorted_counts = np.sort(value_counts.values)
        n = len(sorted_counts)
        cumsum = np.cumsum(sorted_counts)
        gini = (2 * np.sum((np.arange(1, n + 1) * sorted_counts))) / (n * cumsum[-1]) - (n + 1) / n
        results['gini_coefficient'] = gini
    
    return results


def run(df: pd.DataFrame, viz: "Visualizer") -> None:
    """Analyze categorical distributions and create visualizations.
    
    Args:
        df: Input DataFrame
        viz: Visualizer instance
    """
    # Find categorical columns
    categorical_cols = []
    
    # Object/string columns
    object_cols = df.select_dtypes(include=['object']).columns.tolist()
    categorical_cols.extend(object_cols)
    
    # Low-cardinality numeric columns (likely categorical)
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    for col in numeric_cols:
        unique_count = df[col].nunique()
        total_count = df[col].notna().sum()
        if unique_count <= 20 and unique_count / max(total_count, 1) < 0.1:
            categorical_cols.append(col)
    
    # Remove duplicates and derived columns
    exclude_cols = ['Row_Completeness', 'Description_Length', 'Title_Length']
    categorical_cols = list(set(categorical_cols))
    categorical_cols = [col for col in categorical_cols if col not in exclude_cols]
    
    if not categorical_cols:
        viz.text("No categorical columns found for analysis!", 
                win="categorical_summary", title="Categorical Distribution Summary")
        return
    
    # Analyze each categorical column
    analysis_results = {}
    
    for col in categorical_cols:
        if col in df.columns:
            results = analyze_categorical_column(df[col])
            if 'error' not in results and results['unique_count'] > 1:
                analysis_results[col] = results
    
    if not analysis_results:
        viz.text("No suitable categorical data found for analysis!", 
                win="categorical_summary", title="Categorical Distribution Summary")
        return
    
    # Create visualizations for top categorical columns
    # Sort by diversity (entropy) to show most interesting columns first
    sorted_cols = sorted(analysis_results.keys(), 
                        key=lambda x: analysis_results[x].get('entropy', 0), 
                        reverse=True)
    
    # 1. Overview of categorical diversity
    diversity_data = []
    for col in sorted_cols:
        data = analysis_results[col]
        diversity_data.append({
            'Column': col,
            'Unique_Values': data['unique_count'],
            'Entropy': data.get('entropy', 0),
            'Top3_Concentration': data.get('top3_concentration', 0) * 100
        })
    
    diversity_df = pd.DataFrame(diversity_data)
    
    fig_diversity = px.scatter(
        diversity_df,
        x='Unique_Values',
        y='Entropy',
        size='Top3_Concentration',
        hover_data=['Column'],
        title='Categorical Column Diversity Overview',
        labels={
            'Unique_Values': 'Number of Unique Values',
            'Entropy': 'Shannon Entropy (Diversity)',
            'Top3_Concentration': 'Top 3 Concentration %'
        },
        color='Entropy',
        color_continuous_scale='Viridis'
    )
    viz.plotlyplot(fig_diversity, win="categorical_diversity")
    viz.export(fig_diversity, "categorical_diversity")
    
    # 2. Detailed analysis for top 3 most diverse columns
    for i, col in enumerate(sorted_cols[:3]):
        data = analysis_results[col]
        value_counts = data['value_counts']
        
        # Create bar chart for top categories
        top_values = value_counts.head(15)
        
        fig_dist = px.bar(
            x=top_values.index.astype(str),
            y=top_values.values,
            title=f'Distribution: {col} (Top 15 Categories)',
            labels={'x': col, 'y': 'Count'},
            color=top_values.values,
            color_continuous_scale='Blues'
        )
        fig_dist.update_xaxis(tickangle=45)
        viz.plotlyplot(fig_dist, win=f"distribution_{col}")
        viz.export(fig_dist, f"distribution_{col}")
        
        # Create pie chart for top categories
        if len(value_counts) > 1:
            # Group small categories into "Others"
            if len(value_counts) > 10:
                top_10 = value_counts.head(10)
                others_count = value_counts.iloc[10:].sum()
                
                pie_data = pd.concat([
                    top_10,
                    pd.Series([others_count], index=['Others'])
                ])
            else:
                pie_data = value_counts
            
            fig_pie = px.pie(
                values=pie_data.values,
                names=pie_data.index.astype(str),
                title=f'Proportion: {col}',
                color_discrete_sequence=px.colors.qualitative.Set3
            )
            viz.plotlyplot(fig_pie, win=f"pie_{col}")
            viz.export(fig_pie, f"pie_{col}")
    
    # 3. Cardinality analysis
    cardinality_data = pd.DataFrame([
        {
            'Column': col,
            'Cardinality': data['unique_count'],
            'Cardinality_Ratio': data['unique_count'] / data['valid_count'] if data['valid_count'] > 0 else 0,
            'Missing_Percentage': (data['null_count'] / data['total_count'] * 100) if data['total_count'] > 0 else 0
        }
        for col, data in analysis_results.items()
    ]).sort_values('Cardinality', ascending=False)
    
    fig_cardinality = px.bar(
        cardinality_data,
        x='Column',
        y='Cardinality',
        title='Cardinality by Categorical Column',
        labels={'Cardinality': 'Number of Unique Values', 'Column': 'Columns'},
        color='Cardinality_Ratio',
        color_continuous_scale='Oranges'
    )
    fig_cardinality.update_xaxis(tickangle=45)
    viz.plotlyplot(fig_cardinality, win="cardinality_analysis")
    viz.export(fig_cardinality, "cardinality_analysis")
    
    # 4. Missing values in categorical columns
    missing_data = cardinality_data[cardinality_data['Missing_Percentage'] > 0]
    
    if len(missing_data) > 0:
        fig_missing = px.bar(
            missing_data,
            x='Column',
            y='Missing_Percentage',
            title='Missing Values in Categorical Columns',
            labels={'Missing_Percentage': 'Missing %', 'Column': 'Columns'},
            color='Missing_Percentage',
            color_continuous_scale='Reds'
        )
        fig_missing.update_xaxis(tickangle=45)
        viz.plotlyplot(fig_missing, win="categorical_missing")
        viz.export(fig_missing, "categorical_missing")
    
    # Create summary text
    total_categorical = len(analysis_results)
    avg_cardinality = np.mean([data['unique_count'] for data in analysis_results.values()])
    high_cardinality_cols = [col for col, data in analysis_results.items() 
                           if data['unique_count'] > data['valid_count'] * 0.5]
    
    summary_text = f"""
    <h3>Categorical Distribution Summary</h3>
    <p><strong>Categorical columns analyzed:</strong> {total_categorical}</p>
    <p><strong>Average cardinality:</strong> {avg_cardinality:.1f}</p>
    <p><strong>High cardinality columns:</strong> {len(high_cardinality_cols)}</p>
    
    <h4>Column Details:</h4>
    """
    
    for col in sorted_cols[:5]:
        data = analysis_results[col]
        summary_text += f"""
        <p><strong>{col}:</strong></p>
        <ul>
            <li>Unique values: {data['unique_count']:,}</li>
            <li>Most common: "{data['most_common']}" ({data['most_common_count']} occurrences)</li>
            <li>Diversity (entropy): {data.get('entropy', 0):.2f}</li>
            <li>Top 3 concentration: {data.get('top3_concentration', 0)*100:.1f}%</li>
        </ul>
        """
    
    # Add data quality insights
    problematic_cols = []
    for col, data in analysis_results.items():
        if data['null_count'] / data['total_count'] > 0.2:  # >20% missing
            problematic_cols.append(f"{col} ({data['null_count']/data['total_count']*100:.1f}% missing)")
    
    if problematic_cols:
        summary_text += f"""
        <h4>Data Quality Issues:</h4>
        <p><strong>Columns with high missing rates:</strong></p>
        """
        for col_info in problematic_cols:
            summary_text += f"<p>• {col_info}</p>"
    
    viz.text(summary_text, win="categorical_summary", title="Categorical Distribution Summary")
