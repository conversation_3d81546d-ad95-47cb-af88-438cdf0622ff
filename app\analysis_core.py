"""Common statistical utilities for data quality analysis."""

import pandas as pd
import numpy as np
from typing import Union


def field_completion(df: pd.DataFrame) -> pd.Series:
    """Calculate field completion rate as percentage.
    
    Args:
        df: Input DataFrame
        
    Returns:
        pd.Series: Completion rate for each field as percentage
    """
    return (df.notna().mean() * 100).round(2)


def row_completion(df: pd.DataFrame) -> pd.Series:
    """Calculate row-level completion rate as percentage.
    
    Args:
        df: Input DataFrame
        
    Returns:
        pd.Series: Completion rate for each row as percentage
    """
    return (df.notna().mean(axis=1) * 100).round(2)


def iso_outliers(series: pd.Series, contamination: float = 0.02) -> pd.Series:
    """Detect outliers using IsolationForest.
    
    Args:
        series: Input series for outlier detection
        contamination: Expected proportion of outliers
        
    Returns:
        pd.Series: Boolean series where True indicates outlier
    """
    from sklearn.ensemble import IsolationForest
    
    # Handle missing values
    clean_series = series.dropna()
    if len(clean_series) == 0:
        return pd.Series([False] * len(series), index=series.index)
    
    model = IsolationForest(contamination=contamination, random_state=0)
    predictions = model.fit_predict(clean_series.values.reshape(-1, 1))
    
    # Create result series with same index as input
    result = pd.Series([False] * len(series), index=series.index)
    result.loc[clean_series.index] = (predictions == -1)
    
    return result
