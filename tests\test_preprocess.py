"""Tests for the preprocessing module."""

import pytest
import pandas as pd
import numpy as np
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.preprocess import clean, extract_features


class TestPreprocess:
    """Test cases for preprocessing functions."""
    
    def test_clean_basic(self):
        """Test basic data cleaning."""
        df = pd.DataFrame({
            'Title': ['  Book 1  ', 'Book 2', ''],
            'Rating': ['4.5', '3.2', 'invalid'],
            'Year': ['2020', '2021', ''],
            'Publication_Date': ['2020-01-01', '2021-06-15', 'invalid']
        })
        
        df_clean = clean(df)
        
        # Check text cleaning
        assert df_clean['Title'].iloc[0] == 'Book 1'
        assert pd.isna(df_clean['Title'].iloc[2])
        
        # Check numeric conversion
        assert df_clean['Rating'].iloc[0] == 4.5
        assert pd.isna(df_clean['Rating'].iloc[2])
        
        # Check date conversion
        assert pd.notna(df_clean['Publication_Date'].iloc[0])
        assert pd.isna(df_clean['Publication_Date'].iloc[2])
        
        # Check derived features
        assert 'Row_Completeness' in df_clean.columns
    
    def test_clean_with_description(self):
        """Test cleaning with description field."""
        df = pd.DataFrame({
            'Title': ['Book 1', 'Book 2'],
            'Description': ['A great book about adventures', 'Short desc']
        })
        
        df_clean = clean(df)
        
        # Check derived features
        assert 'Description_Length' in df_clean.columns
        assert 'Title_Length' in df_clean.columns
        assert df_clean['Description_Length'].iloc[0] > df_clean['Description_Length'].iloc[1]
    
    def test_extract_features_with_dates(self):
        """Test feature extraction with date columns."""
        df = pd.DataFrame({
            'Publication_Date': pd.to_datetime(['2020-01-15', '2021-06-30']),
            'Title': ['Book One', 'Book Two']
        })
        
        df_features = extract_features(df)
        
        # Check date-derived features
        assert 'Publication_Date_Year' in df_features.columns
        assert 'Publication_Date_Month' in df_features.columns
        assert df_features['Publication_Date_Year'].iloc[0] == 2020
        assert df_features['Publication_Date_Month'].iloc[0] == 1
    
    def test_extract_features_with_text(self):
        """Test feature extraction with text columns."""
        df = pd.DataFrame({
            'Title': ['The Great Book', 'Short'],
            'Description': ['This is a long description with many words', 'Brief'],
            'Genre': ['Science Fiction Fantasy', 'Drama']
        })
        
        df_features = extract_features(df)
        
        # Check text-derived features
        assert 'Title_Word_Count' in df_features.columns
        assert 'Description_Word_Count' in df_features.columns
        assert 'Genre_Word_Count' in df_features.columns
        
        assert df_features['Title_Word_Count'].iloc[0] == 3
        assert df_features['Description_Word_Count'].iloc[0] == 9
    
    def test_clean_empty_dataframe(self):
        """Test cleaning with empty DataFrame."""
        df = pd.DataFrame()
        df_clean = clean(df)
        
        assert len(df_clean) == 0
        assert 'Row_Completeness' in df_clean.columns
    
    def test_clean_preserves_original(self):
        """Test that cleaning doesn't modify original DataFrame."""
        df = pd.DataFrame({
            'Title': ['  Book 1  ', 'Book 2'],
            'Rating': ['4.5', '3.2']
        })
        
        original_title = df['Title'].iloc[0]
        df_clean = clean(df)
        
        # Original should be unchanged
        assert df['Title'].iloc[0] == original_title
        # Cleaned should be different
        assert df_clean['Title'].iloc[0] != original_title
