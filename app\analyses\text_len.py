"""Text length distribution analysis for description fields."""

import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from typing import TYPE_CHECKING, List, Dict

if TYPE_CHECKING:
    from ..visualizer import Visualizer


def analyze_text_field(series: pd.Series, field_name: str) -> Dict:
    """Analyze text length statistics for a field.
    
    Args:
        series: Text series to analyze
        field_name: Name of the field
        
    Returns:
        Dictionary with analysis results
    """
    # Convert to string and calculate lengths
    text_series = series.astype(str)
    
    # Filter out null/empty values
    valid_mask = (series.notna()) & (text_series != 'nan') & (text_series.str.strip() != '')
    valid_texts = text_series[valid_mask]
    
    if len(valid_texts) == 0:
        return {'error': 'No valid text data'}
    
    # Calculate text lengths
    lengths = valid_texts.str.len()
    
    # Calculate word counts
    word_counts = valid_texts.str.split().str.len()
    
    # Calculate character statistics
    results = {
        'field_name': field_name,
        'total_count': len(series),
        'valid_count': len(valid_texts),
        'empty_count': len(series) - len(valid_texts),
        'lengths': lengths,
        'word_counts': word_counts,
        
        # Length statistics
        'min_length': lengths.min(),
        'max_length': lengths.max(),
        'mean_length': lengths.mean(),
        'median_length': lengths.median(),
        'std_length': lengths.std(),
        
        # Word count statistics
        'min_words': word_counts.min(),
        'max_words': word_counts.max(),
        'mean_words': word_counts.mean(),
        'median_words': word_counts.median(),
        
        # Percentiles
        'length_25th': lengths.quantile(0.25),
        'length_75th': lengths.quantile(0.75),
        'length_95th': lengths.quantile(0.95),
        'length_99th': lengths.quantile(0.99),
    }
    
    # Identify potential issues
    results['very_short'] = (lengths <= 10).sum()  # <= 10 characters
    results['very_long'] = (lengths >= lengths.quantile(0.99)).sum()  # Top 1%
    results['single_word'] = (word_counts == 1).sum()
    results['no_words'] = (word_counts == 0).sum()
    
    # Calculate length categories
    length_categories = pd.cut(
        lengths,
        bins=[0, 50, 100, 200, 500, 1000, float('inf')],
        labels=['Very Short (0-50)', 'Short (50-100)', 'Medium (100-200)', 
               'Long (200-500)', 'Very Long (500-1000)', 'Extremely Long (1000+)'],
        include_lowest=True
    )
    results['length_categories'] = length_categories.value_counts()
    
    return results


def run(df: pd.DataFrame, viz: "Visualizer") -> None:
    """Analyze text length distributions and create visualizations.
    
    Args:
        df: Input DataFrame
        viz: Visualizer instance
    """
    # Find text fields to analyze
    text_fields = []
    
    # Common text field names
    text_field_names = ['description', 'summary', 'abstract', 'content', 'text', 
                       'title', 'name', 'comment', 'review', 'notes', 'bio']
    
    for col in df.columns:
        if any(field_name in col.lower() for field_name in text_field_names):
            if df[col].dtype == 'object':  # String/object columns
                text_fields.append(col)
    
    # Also include any object columns that might contain substantial text
    for col in df.select_dtypes(include=['object']).columns:
        if col not in text_fields:
            # Check if this column contains substantial text (avg length > 20)
            sample_texts = df[col].dropna().astype(str)
            if len(sample_texts) > 0:
                avg_length = sample_texts.str.len().mean()
                if avg_length > 20:
                    text_fields.append(col)
    
    if not text_fields:
        viz.text("No text fields found for length analysis!", 
                win="text_length_summary", title="Text Length Analysis Summary")
        return
    
    # Analyze each text field
    analysis_results = {}
    
    for field in text_fields:
        if field in df.columns:
            results = analyze_text_field(df[field], field)
            if 'error' not in results:
                analysis_results[field] = results
    
    if not analysis_results:
        viz.text("No valid text data found for analysis!", 
                win="text_length_summary", title="Text Length Analysis Summary")
        return
    
    # Create visualizations
    
    # 1. Length distribution comparison
    if len(analysis_results) > 1:
        comparison_data = []
        for field, data in analysis_results.items():
            comparison_data.append({
                'Field': field,
                'Mean_Length': data['mean_length'],
                'Median_Length': data['median_length'],
                'Max_Length': data['max_length'],
                'Valid_Count': data['valid_count']
            })
        
        comparison_df = pd.DataFrame(comparison_data)
        
        fig_comparison = px.bar(
            comparison_df,
            x='Field',
            y='Mean_Length',
            title='Average Text Length by Field',
            labels={'Mean_Length': 'Average Length (characters)', 'Field': 'Text Fields'},
            color='Mean_Length',
            color_continuous_scale='Blues'
        )
        fig_comparison.update_xaxis(tickangle=45)
        viz.plotlyplot(fig_comparison, win="length_comparison")
        viz.export(fig_comparison, "text_length_comparison")
    
    # 2. Detailed analysis for primary text field
    primary_field = max(analysis_results.keys(), 
                       key=lambda x: analysis_results[x]['mean_length'])
    primary_data = analysis_results[primary_field]
    
    # Length distribution histogram
    fig_hist = px.histogram(
        x=primary_data['lengths'],
        nbins=50,
        title=f'Text Length Distribution: {primary_field}',
        labels={'x': 'Text Length (characters)', 'y': 'Frequency'},
        color_discrete_sequence=['skyblue']
    )
    
    # Add statistical lines
    fig_hist.add_vline(x=primary_data['mean_length'], line_dash="dash", 
                      line_color="red", annotation_text="Mean")
    fig_hist.add_vline(x=primary_data['median_length'], line_dash="dash", 
                      line_color="green", annotation_text="Median")
    
    viz.plotlyplot(fig_hist, win="length_distribution")
    viz.export(fig_hist, "text_length_distribution")
    
    # 3. Length categories pie chart
    if 'length_categories' in primary_data:
        categories = primary_data['length_categories']
        
        fig_pie = px.pie(
            values=categories.values,
            names=categories.index,
            title=f'Text Length Categories: {primary_field}',
            color_discrete_sequence=px.colors.qualitative.Set3
        )
        viz.plotlyplot(fig_pie, win="length_categories")
        viz.export(fig_pie, "text_length_categories")
    
    # 4. Word count analysis
    fig_words = px.histogram(
        x=primary_data['word_counts'],
        nbins=30,
        title=f'Word Count Distribution: {primary_field}',
        labels={'x': 'Word Count', 'y': 'Frequency'},
        color_discrete_sequence=['lightcoral']
    )
    
    fig_words.add_vline(x=primary_data['mean_words'], line_dash="dash", 
                       line_color="red", annotation_text="Mean")
    fig_words.add_vline(x=primary_data['median_words'], line_dash="dash", 
                       line_color="green", annotation_text="Median")
    
    viz.plotlyplot(fig_words, win="word_distribution")
    viz.export(fig_words, "word_count_distribution")
    
    # 5. Length vs Word Count scatter plot
    if len(primary_data['lengths']) > 0:
        scatter_df = pd.DataFrame({
            'Length': primary_data['lengths'],
            'Word_Count': primary_data['word_counts']
        })
        
        fig_scatter = px.scatter(
            scatter_df.sample(min(1000, len(scatter_df))),  # Sample for performance
            x='Length',
            y='Word_Count',
            title=f'Text Length vs Word Count: {primary_field}',
            labels={'Length': 'Character Length', 'Word_Count': 'Word Count'},
            opacity=0.6
        )
        
        # Add trend line
        fig_scatter.add_trace(
            go.Scatter(
                x=scatter_df['Length'],
                y=scatter_df['Length'] / 5,  # Rough estimate: 5 chars per word
                mode='lines',
                name='Expected Ratio (5 chars/word)',
                line=dict(dash='dash', color='red')
            )
        )
        
        viz.plotlyplot(fig_scatter, win="length_vs_words")
        viz.export(fig_scatter, "length_vs_words")
    
    # 6. Quality issues analysis
    quality_issues = []
    for field, data in analysis_results.items():
        issues = {
            'Field': field,
            'Empty_Texts': data['empty_count'],
            'Very_Short': data['very_short'],
            'Very_Long': data['very_long'],
            'Single_Word': data['single_word'],
            'No_Words': data['no_words']
        }
        quality_issues.append(issues)
    
    quality_df = pd.DataFrame(quality_issues)
    
    # Melt for visualization
    quality_melted = quality_df.melt(
        id_vars=['Field'],
        value_vars=['Empty_Texts', 'Very_Short', 'Very_Long', 'Single_Word', 'No_Words'],
        var_name='Issue_Type',
        value_name='Count'
    )
    
    fig_quality = px.bar(
        quality_melted,
        x='Field',
        y='Count',
        color='Issue_Type',
        title='Text Quality Issues by Field',
        labels={'Count': 'Number of Records', 'Field': 'Text Fields'},
        barmode='group'
    )
    fig_quality.update_xaxis(tickangle=45)
    viz.plotlyplot(fig_quality, win="text_quality_issues")
    viz.export(fig_quality, "text_quality_issues")
    
    # Create summary text
    total_fields = len(analysis_results)
    
    summary_text = f"""
    <h3>Text Length Analysis Summary</h3>
    <p><strong>Text fields analyzed:</strong> {total_fields}</p>
    
    <h4>Primary Field Analysis: {primary_field}</h4>
    <p><strong>Valid texts:</strong> {primary_data['valid_count']:,}</p>
    <p><strong>Empty/missing:</strong> {primary_data['empty_count']:,}</p>
    
    <h4>Length Statistics (characters):</h4>
    <p><strong>Average:</strong> {primary_data['mean_length']:.1f}</p>
    <p><strong>Median:</strong> {primary_data['median_length']:.1f}</p>
    <p><strong>Range:</strong> {primary_data['min_length']} - {primary_data['max_length']:,}</p>
    <p><strong>95th percentile:</strong> {primary_data['length_95th']:.1f}</p>
    
    <h4>Word Count Statistics:</h4>
    <p><strong>Average words:</strong> {primary_data['mean_words']:.1f}</p>
    <p><strong>Median words:</strong> {primary_data['median_words']:.1f}</p>
    <p><strong>Range:</strong> {primary_data['min_words']} - {primary_data['max_words']}</p>
    
    <h4>Quality Issues:</h4>
    <p><strong>Very short texts (≤10 chars):</strong> {primary_data['very_short']} ({primary_data['very_short']/primary_data['valid_count']*100:.1f}%)</p>
    <p><strong>Very long texts (top 1%):</strong> {primary_data['very_long']}</p>
    <p><strong>Single word entries:</strong> {primary_data['single_word']} ({primary_data['single_word']/primary_data['valid_count']*100:.1f}%)</p>
    """
    
    # Add comparison with other fields
    if len(analysis_results) > 1:
        summary_text += f"""
        <h4>Field Comparison:</h4>
        """
        for field, data in analysis_results.items():
            if field != primary_field:
                summary_text += f"<p>• {field}: avg {data['mean_length']:.1f} chars, {data['valid_count']:,} texts</p>"
    
    viz.text(summary_text, win="text_length_summary", title="Text Length Analysis Summary")
