"""Tests for the analysis core module."""

import pytest
import pandas as pd
import numpy as np
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.analysis_core import field_completion, row_completion, iso_outliers


class TestAnalysisCore:
    """Test cases for analysis core functions."""
    
    def test_field_completion(self):
        """Test field completion calculation."""
        df = pd.DataFrame({
            'A': [1, 2, np.nan, 4],
            'B': [1, np.nan, np.nan, 4],
            'C': [1, 2, 3, 4]
        })
        
        completion = field_completion(df)
        
        assert completion['A'] == 75.0  # 3/4 * 100
        assert completion['B'] == 50.0  # 2/4 * 100
        assert completion['C'] == 100.0  # 4/4 * 100
    
    def test_row_completion(self):
        """Test row completion calculation."""
        df = pd.DataFrame({
            'A': [1, 2, np.nan],
            'B': [1, np.nan, np.nan],
            'C': [1, 2, 3]
        })
        
        completion = row_completion(df)
        
        assert completion.iloc[0] == 100.0  # 3/3 * 100
        assert completion.iloc[1] == 66.67  # 2/3 * 100, rounded
        assert completion.iloc[2] == 33.33  # 1/3 * 100, rounded
    
    def test_iso_outliers_normal_data(self):
        """Test outlier detection with normal data."""
        # Create data with clear outliers
        normal_data = np.random.normal(0, 1, 100)
        outliers = np.array([10, -10, 15])
        data = np.concatenate([normal_data, outliers])
        
        series = pd.Series(data)
        outlier_mask = iso_outliers(series, contamination=0.05)
        
        # Should detect some outliers
        assert outlier_mask.sum() > 0
        assert outlier_mask.sum() <= len(data) * 0.1  # Reasonable number
    
    def test_iso_outliers_empty_series(self):
        """Test outlier detection with empty series."""
        series = pd.Series([np.nan, np.nan, np.nan])
        outlier_mask = iso_outliers(series)
        
        # Should return all False for empty series
        assert not outlier_mask.any()
        assert len(outlier_mask) == len(series)
    
    def test_iso_outliers_single_value(self):
        """Test outlier detection with single value."""
        series = pd.Series([1.0])
        outlier_mask = iso_outliers(series)
        
        # Single value cannot be an outlier
        assert not outlier_mask.any()
    
    def test_field_completion_empty_dataframe(self):
        """Test field completion with empty DataFrame."""
        df = pd.DataFrame()
        completion = field_completion(df)
        
        assert len(completion) == 0
    
    def test_row_completion_empty_dataframe(self):
        """Test row completion with empty DataFrame."""
        df = pd.DataFrame()
        completion = row_completion(df)
        
        assert len(completion) == 0
